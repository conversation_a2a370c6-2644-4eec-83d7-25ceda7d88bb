@extends('layouts.app')

@section('title', 'إدارة الإجازات')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4 animate-fade-in-down">
    <div>
        <h1 class="h3 mb-2 gradient-text fw-bold">
            <i class="bi bi-calendar-check me-2"></i>
            إدارة الإجازات
        </h1>
        <p class="text-muted mb-0">إدارة طلبات الإجازات والموافقات</p>
    </div>
    <div class="btn-group">
        <a href="{{ route('leaves.create') }}" class="btn btn-premium">
            <i class="bi bi-plus-circle me-2"></i>
            طلب إجازة جديد
        </a>
        <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#bulkApprovalModal">
            <i class="bi bi-check-all me-2"></i>
            موافقة جماعية
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.1s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-warning mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        طلبات معلقة
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark">
                        {{ $leaves->where('status', 'pending')->count() }}
                    </div>
                </div>
                <div class="bg-warning bg-gradient rounded-circle p-3 animate-float">
                    <i class="bi bi-clock-fill text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.2s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-success mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        طلبات موافق عليها
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark">
                        {{ $leaves->where('status', 'approved')->count() }}
                    </div>
                </div>
                <div class="bg-success bg-gradient rounded-circle p-3 animate-float" style="animation-delay: 0.5s;">
                    <i class="bi bi-check-circle-fill text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.3s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-danger mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        طلبات مرفوضة
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark">
                        {{ $leaves->where('status', 'rejected')->count() }}
                    </div>
                </div>
                <div class="bg-danger bg-gradient rounded-circle p-3 animate-float" style="animation-delay: 1s;">
                    <i class="bi bi-x-circle-fill text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.4s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-info mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        إجمالي الأيام
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark">
                        {{ $leaves->sum('days_requested') }}
                    </div>
                </div>
                <div class="bg-info bg-gradient rounded-circle p-3 animate-float" style="animation-delay: 1.5s;">
                    <i class="bi bi-calendar-range text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="premium-card mb-4 animate-fade-in-up" style="animation-delay: 0.5s;">
    <div class="card-body">
        <form method="GET" action="{{ route('leaves.index') }}" class="row g-3">
            <div class="col-md-3">
                <label for="status_filter" class="form-label fw-semibold">الحالة</label>
                <select class="form-select" id="status_filter" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>معلق</option>
                    <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>موافق عليه</option>
                    <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="type_filter" class="form-label fw-semibold">نوع الإجازة</label>
                <select class="form-select" id="type_filter" name="type">
                    <option value="">جميع الأنواع</option>
                    <option value="annual" {{ request('type') == 'annual' ? 'selected' : '' }}>سنوية</option>
                    <option value="sick" {{ request('type') == 'sick' ? 'selected' : '' }}>مرضية</option>
                    <option value="emergency" {{ request('type') == 'emergency' ? 'selected' : '' }}>طارئة</option>
                    <option value="maternity" {{ request('type') == 'maternity' ? 'selected' : '' }}>أمومة</option>
                    <option value="paternity" {{ request('type') == 'paternity' ? 'selected' : '' }}>أبوة</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="employee_filter" class="form-label fw-semibold">الموظف</label>
                <select class="form-select select2" id="employee_filter" name="employee_id">
                    <option value="">جميع الموظفين</option>
                    @foreach(\App\Models\Employee::where('employment_status', 'active')->get() as $employee)
                        <option value="{{ $employee->id }}" {{ request('employee_id') == $employee->id ? 'selected' : '' }}>
                            {{ $employee->full_name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="bi bi-funnel me-1"></i>
                    تصفية
                </button>
                <a href="{{ route('leaves.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-x-circle me-1"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Leaves Table -->
<div class="premium-card animate-fade-in-up" style="animation-delay: 0.6s;">
    <div class="card-header bg-transparent border-0 py-4">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0 fw-bold gradient-text">
                <i class="bi bi-table me-2"></i>
                سجلات الإجازات
            </h5>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-download me-1"></i>
                    تصدير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#"><i class="bi bi-file-excel me-2"></i>Excel</a></li>
                    <li><a class="dropdown-item" href="#"><i class="bi bi-file-pdf me-2"></i>PDF</a></li>
                    <li><a class="dropdown-item" href="#"><i class="bi bi-printer me-2"></i>طباعة</a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card-body pt-0">
        <div class="table-responsive">
            <table class="table table-hover" id="leavesTable">
                <thead class="table-light">
                    <tr>
                        <th>
                            <input type="checkbox" class="form-check-input" id="selectAll">
                        </th>
                        <th>الموظف</th>
                        <th>نوع الإجازة</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>عدد الأيام</th>
                        <th>الحالة</th>
                        <th>تاريخ الطلب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($leaves as $leave)
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input leave-checkbox" value="{{ $leave->id }}">
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px;">
                                    <i class="bi bi-person text-white"></i>
                                </div>
                                <div>
                                    <div class="fw-semibold">{{ $leave->employee->full_name }}</div>
                                    <small class="text-muted">{{ $leave->employee->employee_id }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark">
                                @switch($leave->type)
                                    @case('annual')
                                        <i class="bi bi-calendar-heart me-1"></i>سنوية
                                        @break
                                    @case('sick')
                                        <i class="bi bi-heart-pulse me-1"></i>مرضية
                                        @break
                                    @case('emergency')
                                        <i class="bi bi-exclamation-triangle me-1"></i>طارئة
                                        @break
                                    @case('maternity')
                                        <i class="bi bi-person-hearts me-1"></i>أمومة
                                        @break
                                    @case('paternity')
                                        <i class="bi bi-person-check me-1"></i>أبوة
                                        @break
                                    @default
                                        {{ $leave->type }}
                                @endswitch
                            </span>
                        </td>
                        <td>{{ $leave->start_date->format('Y/m/d') }}</td>
                        <td>{{ $leave->end_date->format('Y/m/d') }}</td>
                        <td>
                            <span class="badge bg-info">{{ $leave->days_requested }} يوم</span>
                        </td>
                        <td>
                            @switch($leave->status)
                                @case('pending')
                                    <span class="badge bg-warning">
                                        <i class="bi bi-clock me-1"></i>معلق
                                    </span>
                                    @break
                                @case('approved')
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle me-1"></i>موافق عليه
                                    </span>
                                    @break
                                @case('rejected')
                                    <span class="badge bg-danger">
                                        <i class="bi bi-x-circle me-1"></i>مرفوض
                                    </span>
                                    @break
                                @default
                                    <span class="badge bg-secondary">{{ $leave->status }}</span>
                            @endswitch
                        </td>
                        <td>
                            <small class="text-muted">{{ $leave->created_at->format('Y/m/d H:i') }}</small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('leaves.show', $leave) }}" 
                                   class="btn btn-sm btn-outline-info" 
                                   title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @if($leave->status === 'pending')
                                    <a href="{{ route('leaves.edit', $leave) }}" 
                                       class="btn btn-sm btn-outline-primary" 
                                       title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-success" 
                                            onclick="approveLeave({{ $leave->id }})"
                                            title="موافقة">
                                        <i class="bi bi-check"></i>
                                    </button>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger" 
                                            onclick="rejectLeave({{ $leave->id }})"
                                            title="رفض">
                                        <i class="bi bi-x"></i>
                                    </button>
                                @endif
                                <button type="button" 
                                        class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteLeave({{ $leave->id }})"
                                        title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="9" class="text-center py-5">
                            <div class="text-muted">
                                <i class="bi bi-inbox fs-1 d-block mb-3"></i>
                                <h6>لا توجد طلبات إجازات</h6>
                                <p class="mb-0">لم يتم العثور على أي طلبات إجازات</p>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        @if($leaves->hasPages())
        <div class="d-flex justify-content-center mt-4">
            {{ $leaves->links() }}
        </div>
        @endif
    </div>
</div>

<!-- Bulk Approval Modal -->
<div class="modal fade" id="bulkApprovalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">موافقة جماعية على الطلبات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من الموافقة على جميع الطلبات المحددة؟</p>
                <div id="selectedLeavesCount" class="alert alert-info">
                    لم يتم تحديد أي طلبات
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="bulkApprove()">موافقة</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#leavesTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
        },
        order: [[7, 'desc']], // Order by date created
        columnDefs: [
            { orderable: false, targets: [0, 8] } // Disable ordering for checkbox and actions
        ]
    });

    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap-5',
        placeholder: 'اختر الموظف',
        allowClear: true
    });

    // Select All functionality
    $('#selectAll').on('change', function() {
        $('.leave-checkbox').prop('checked', $(this).is(':checked'));
        updateBulkActionButton();
    });

    // Individual checkbox change
    $('.leave-checkbox').on('change', function() {
        updateBulkActionButton();

        // Update select all checkbox
        const totalCheckboxes = $('.leave-checkbox').length;
        const checkedCheckboxes = $('.leave-checkbox:checked').length;

        $('#selectAll').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
        $('#selectAll').prop('checked', checkedCheckboxes === totalCheckboxes);
    });

    function updateBulkActionButton() {
        const checkedCount = $('.leave-checkbox:checked').length;
        $('#selectedLeavesCount').text(checkedCount > 0 ?
            `تم تحديد ${checkedCount} طلب` :
            'لم يتم تحديد أي طلبات'
        );
    }
});

// Approve Leave Function
function approveLeave(leaveId) {
    Swal.fire({
        title: 'موافقة على الطلب',
        text: 'هل أنت متأكد من الموافقة على هذا الطلب؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، وافق',
        cancelButtonText: 'إلغاء',
        input: 'textarea',
        inputPlaceholder: 'ملاحظات الموافقة (اختياري)...',
        inputAttributes: {
            'aria-label': 'ملاحظات الموافقة'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Send approval request
            fetch(`/leaves/${leaveId}/approve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    approval_notes: result.value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم!', data.message, 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ!', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.fire('خطأ!', 'حدث خطأ في الشبكة', 'error');
            });
        }
    });
}

// Reject Leave Function
function rejectLeave(leaveId) {
    Swal.fire({
        title: 'رفض الطلب',
        text: 'هل أنت متأكد من رفض هذا الطلب؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، ارفض',
        cancelButtonText: 'إلغاء',
        input: 'textarea',
        inputPlaceholder: 'سبب الرفض (مطلوب)...',
        inputAttributes: {
            'aria-label': 'سبب الرفض'
        },
        inputValidator: (value) => {
            if (!value) {
                return 'يرجى كتابة سبب الرفض'
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Send rejection request
            fetch(`/leaves/${leaveId}/reject`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    approval_notes: result.value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم!', data.message, 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ!', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.fire('خطأ!', 'حدث خطأ في الشبكة', 'error');
            });
        }
    });
}

// Delete Leave Function
function deleteLeave(leaveId) {
    Swal.fire({
        title: 'حذف الطلب',
        text: 'هل أنت متأكد من حذف هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/leaves/${leaveId}`;

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            const methodField = document.createElement('input');
            methodField.type = 'hidden';
            methodField.name = '_method';
            methodField.value = 'DELETE';

            form.appendChild(csrfToken);
            form.appendChild(methodField);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

// Bulk Approve Function
function bulkApprove() {
    const selectedLeaves = $('.leave-checkbox:checked').map(function() {
        return $(this).val();
    }).get();

    if (selectedLeaves.length === 0) {
        Swal.fire('تنبيه', 'يرجى تحديد طلبات للموافقة عليها', 'warning');
        return;
    }

    Swal.fire({
        title: 'موافقة جماعية',
        text: `سيتم الموافقة على ${selectedLeaves.length} طلب`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'موافقة',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // Process bulk approval
            let completed = 0;
            const total = selectedLeaves.length;

            selectedLeaves.forEach(leaveId => {
                fetch(`/leaves/${leaveId}/approve`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        approval_notes: 'موافقة جماعية'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    completed++;
                    if (completed === total) {
                        Swal.fire('تم!', `تم الموافقة على ${total} طلب بنجاح`, 'success').then(() => {
                            location.reload();
                        });
                    }
                })
                .catch(error => {
                    completed++;
                    if (completed === total) {
                        Swal.fire('تحذير', 'تم الانتهاء مع وجود بعض الأخطاء', 'warning').then(() => {
                            location.reload();
                        });
                    }
                });
            });

            $('#bulkApprovalModal').modal('hide');
        }
    });
}
</script>
@endpush

@push('styles')
<style>
.stats-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.premium-card {
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
    border-radius: 15px;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.btn-group .btn {
    margin: 0 1px;
}

.badge {
    font-size: 11px;
    padding: 6px 10px;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}
</style>
@endpush
