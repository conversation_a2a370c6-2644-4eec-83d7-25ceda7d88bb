# 🗄️ دليل ربط قاعدة البيانات MySQL مع XAMPP

## 📋 المتطلبات المسبقة

1. **XAMPP مثبت ويعمل**
   - Apache Server: ✅ Running
   - MySQL Database: ✅ Running

2. **phpMyAdmin متاح**
   - الرابط: `http://localhost/phpmyadmin`

## 🚀 خطوات الإعداد

### الخطوة 1: إنشاء قاعدة البيانات

1. افتح phpMyAdmin: `http://localhost/phpmyadmin`
2. انقر على "New" في الشريط الجانبي
3. أدخل اسم قاعدة البيانات: `hrms_system`
4. اختر Collation: `utf8mb4_unicode_ci`
5. انقر "Create"

**أو استخدم SQL Command:**
```sql
CREATE DATABASE IF NOT EXISTS `hrms_system` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;
```

### الخطوة 2: تحديث إعدادات Laravel

تم تحديث ملف `.env` تلقائياً بالإعدادات التالية:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hrms_system
DB_USERNAME=root
DB_PASSWORD=
```

### الخطوة 3: تشغيل الهجرات

```bash
# مسح الكاش
php artisan config:clear
php artisan cache:clear

# تشغيل الهجرات
php artisan migrate

# إضافة البيانات التجريبية
php artisan db:seed
```

### الخطوة 4: تشغيل النظام

```bash
php artisan serve
```

ثم افتح المتصفح على: `http://localhost:8000`

## 🔐 بيانات الدخول

### مدير النظام
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456

### موظف الموارد البشرية
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456

## 🛠️ استكشاف الأخطاء

### خطأ الاتصال بقاعدة البيانات

**المشكلة**: `SQLSTATE[HY000] [1049] Unknown database 'hrms_system'`

**الحل**:
1. تأكد من إنشاء قاعدة البيانات في phpMyAdmin
2. تحقق من اسم قاعدة البيانات في ملف `.env`

### خطأ في كلمة المرور

**المشكلة**: `SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost'`

**الحل**:
1. تأكد من أن MySQL يعمل في XAMPP
2. تحقق من كلمة مرور MySQL (افتراضياً فارغة في XAMPP)

### خطأ في المنفذ

**المشكلة**: `SQLSTATE[HY000] [2002] Connection refused`

**الحل**:
1. تأكد من تشغيل MySQL في XAMPP
2. تحقق من رقم المنفذ (افتراضياً 3306)

## 📊 التحقق من الجداول

بعد تشغيل الهجرات، ستجد الجداول التالية في phpMyAdmin:

- `users` - المستخدمين
- `roles` - الأدوار
- `departments` - الأقسام
- `employees` - الموظفين
- `attendances` - الحضور والانصراف
- `leaves` - الإجازات
- `payrolls` - المرتبات
- `audit_logs` - سجل المراجعة
- `performance_reviews` - تقييم الأداء
- `notifications` - الإشعارات

## 🔄 التبديل بين SQLite و MySQL

### للعودة إلى SQLite:
```env
DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=hrms_system
# DB_USERNAME=root
# DB_PASSWORD=
```

### للاستمرار مع MySQL:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hrms_system
DB_USERNAME=root
DB_PASSWORD=
```

## 🚀 سكريبت التهيئة السريع

يمكنك استخدام الملف `setup-mysql.bat` لتهيئة كل شيء تلقائياً:

```bash
# في Windows
setup-mysql.bat

# أو يدوياً
php artisan config:clear
php artisan migrate
php artisan db:seed
php artisan serve
```

## 📝 ملاحظات مهمة

1. **النسخ الاحتياطي**: احرص على عمل نسخة احتياطية من قاعدة البيانات بانتظام
2. **الأمان**: في البيئة الإنتاجية، استخدم كلمة مرور قوية لقاعدة البيانات
3. **الأداء**: MySQL أسرع من SQLite للتطبيقات الكبيرة
4. **التطوير**: يمكن استخدام SQLite للتطوير و MySQL للإنتاج

---

**تم إعداد النظام بنجاح للعمل مع MySQL في XAMPP!** 🎉
