# 🏢 نظام إدارة الموارد البشرية (HRMS) - الإصدار المحسن ✨

نظام إدارة موارد بشرية متكامل، أنيق، سريع، وآمن مبني باستخدام Laravel Framework مع تصميم احترافي وتجربة مستخدم متقدمة.

## 🎨 التحسينات الجديدة في الإصدار المحسن

### 🎯 تصميم احترافي وأنيق:
- **لوجو مخصص**: تصميم لوجو احترافي بتقنية SVG مع تدرجات لونية متقدمة
- **واجهة مستخدم متطورة**: تصميم Glass Morphism مع تأثيرات بصرية متقدمة
- **نظام ألوان متدرج**: استخدام تدرجات لونية احترافية (#667eea, #764ba2, #f093fb)
- **رسوم متحركة سلسة**: انتقالات وحركات متقدمة باستخدام CSS3 و JavaScript

### 🚀 تجربة مستخدم محسنة:
- **صفحة تسجيل دخول متطورة**: تصميم جديد بالكامل مع خلفية متحركة وتأثيرات بصرية
- **لوحة تحكم تفاعلية**: إحصائيات متحركة ومخططات بيانية ملونة
- **بطاقات إحصائيات ذكية**: عدادات متحركة مع تأثيرات hover متقدمة
- **شريط جانبي محسن**: تصميم جديد مع أيقونات محدثة وتأثيرات انتقال

### 🎭 تأثيرات بصرية متقدمة:
- **Ripple Effect**: تأثير الموجة عند النقر على الأزرار
- **Parallax Scrolling**: تأثير المنظور عند التمرير
- **Hover Animations**: تأثيرات تفاعلية عند التمرير فوق العناصر
- **Loading States**: حالات تحميل متحركة وجذابة

### 🌙 الوضع الداكن المحسن:
- **انتقال سلس**: تبديل سلس بين الوضع الفاتح والداكن
- **حفظ التفضيلات**: تذكر اختيار المستخدم تلقائياً
- **تصميم متوافق**: جميع العناصر محسنة للوضع الداكن

## ✨ المميزات الرئيسية

### 🧠 نظام ذكي متعدد الوحدات:
- 📊 **لوحة تحكم تفاعلية**: مخططات ملونة، رسوم بيانية ديناميكية، الوضع الداكن
- 👥 **إدارة الموظفين**: ملفات شخصية، أقسام، وظائف، فلترة متقدمة
- ⏰ **الحضور والانصراف**: تسجيل الوقت الفعلي، تقويم الحضور، QR Code
- 🏖️ **نظام الإجازات**: طلبات، اعتمادات متعددة المستويات، رصيد ديناميكي
- 💰 **المرتبات**: حاسبة تلقائية، إيصالات PDF، تقارير مالية
- 🔐 **نظام الصلاحيات**: RBAC متقدم، أدوار مخصصة
- 🔔 **الإشعارات**: Toast notifications، بريد إلكتروني
- 📈 **تقييم الأداء**: KPIs، تقارير سنوية

## 🛠️ التقنيات المستخدمة

### Backend
- **Laravel Framework** (PHP 8.2+)
- **SQLite Database** (قابل للتغيير إلى MySQL)
- **Eloquent ORM**

### Frontend
- **Bootstrap 5** + **Tailwind CSS**
- **Chart.js** للرسوم البيانية
- **SweetAlert2** للنوافذ التفاعلية
- **DataTables** للجداول المتقدمة
- **Select2** للقوائم المنسدلة
- **Flatpickr** لتحديد التاريخ

## 🚀 التثبيت والإعداد

### متطلبات النظام
- PHP 8.2 أو أحدث
- Composer

### خطوات التثبيت

1. **تثبيت التبعيات**
```bash
composer install
```

2. **إعداد قاعدة البيانات**
```bash
php artisan migrate
php artisan db:seed
```

3. **تشغيل الخادم**
```bash
php artisan serve
```

## 👤 بيانات الدخول الافتراضية

### مدير النظام
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** 123456

### موظف الموارد البشرية
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** 123456

## 🌈 التحسينات في الواجهة

| العنصر | التحسين |
|---------|----------|
| النوافذ | SweetAlert2 للنوافذ التفاعلية |
| النماذج | AJAX + إدخال مباشر |
| الجداول | DataTables + فلترة متقدمة |
| الوضع الليلي | دعم كامل مع حفظ التفضيل |
| الترجمة | دعم العربية والإنجليزية |

## 🛡️ تحسينات الأمان

- ✅ تسجيل دخول محمي بجلسات وTimeout
- ✅ حماية CSRF لجميع النماذج
- ✅ تشفير بيانات حساسة AES-256 وpassword_hash
- ✅ حماية الملفات الخاصة من الوصول المباشر
- ✅ سجل دخول (Audit Trail) لعرض محاولات الدخول

## 📁 هيكل المشروع

```
hrms-system/
├── app/
│   ├── Http/Controllers/
│   ├── Models/
│   └── Middleware/
├── database/
│   ├── migrations/
│   └── seeders/
├── resources/views/
│   ├── layouts/
│   ├── auth/
│   ├── dashboard/
│   └── employees/
└── routes/web.php
```

## 🔧 الميزات المتقدمة

### تخصيص الألوان
```css
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
}
```

### إعداد البريد الإلكتروني
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
```

## 📱 الاستجابة والتوافق

- ✅ تصميم متجاوب (Responsive Design)
- ✅ دعم جميع المتصفحات الحديثة
- ✅ دعم الأجهزة المحمولة
- ✅ دعم اللغة العربية (RTL)

## 🚀 الميزات القادمة

- [ ] تطبيق الهاتف المحمول
- [ ] تكامل مع أنظمة خارجية
- [ ] تقارير متقدمة
- [ ] نظام المهام والمشاريع
- [ ] دردشة داخلية

## 🛠️ التقنيات المستخدمة في الإصدار المحسن

### Backend Framework:
- **Laravel 11**: أحدث إصدار من Laravel Framework
- **PHP 8.2+**: أحدث إصدار من PHP مع الميزات المتقدمة
- **Eloquent ORM**: لإدارة قاعدة البيانات بكفاءة

### Frontend Technologies:
- **Bootstrap 5**: أحدث إصدار من Bootstrap للتصميم المتجاوب
- **jQuery 3.6**: لإدارة DOM والتفاعلات
- **Chart.js**: مخططات بيانية تفاعلية وملونة
- **CSS3 Advanced**: تأثيرات متقدمة مع Backdrop Filter و Gradients

### Premium Enhancements:
- **Custom CSS Framework**: نظام ألوان وتصميم مخصص
- **Advanced Animations**: رسوم متحركة باستخدام CSS3 و JavaScript
- **Glass Morphism**: تأثيرات زجاجية متقدمة
- **Premium Logo**: لوجو مخصص بتقنية SVG مع تدرجات لونية

## 🎨 ملفات التصميم المضافة

```
public/assets/
├── css/
│   └── premium-style.css      # ملف الأنماط المتقدمة
├── js/
│   └── premium-effects.js     # ملف التأثيرات التفاعلية
└── images/
    ├── logo.svg              # اللوجو الرئيسي
    └── icon.svg              # الأيقونة المبسطة
```

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- فتح issue في GitHub
- التواصل عبر البريد الإلكتروني

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

**تم تطوير هذا النظام بعناية فائقة لضمان الأداء العالي والأمان المتقدم مع تصميم احترافي وأنيق** ✨🚀

© 2025 نظام إدارة الموارد البشرية - الإصدار المحسن
