-- إعادة تعيين قاعدة البيانات
-- Reset Database Script

USE hrms_system;

-- تعطيل فحص Foreign Keys مؤقتاً
SET FOREIGN_KEY_CHECKS = 0;

-- حذف جميع الجداول إذا كانت موجودة
DROP TABLE IF EXISTS `performance_reviews`;
DROP TABLE IF EXISTS `audit_logs`;
DROP TABLE IF EXISTS `notifications`;
DROP TABLE IF EXISTS `payrolls`;
DROP TABLE IF EXISTS `leaves`;
DROP TABLE IF EXISTS `attendances`;
DROP TABLE IF EXISTS `employees`;
DROP TABLE IF EXISTS `departments`;
DROP TABLE IF EXISTS `users`;
DROP TABLE IF EXISTS `roles`;
DROP TABLE IF EXISTS `migrations`;
DROP TABLE IF EXISTS `cache`;
DROP TABLE IF EXISTS `cache_locks`;
DROP TABLE IF EXISTS `jobs`;
DROP TABLE IF EXISTS `job_batches`;
DROP TABLE IF EXISTS `failed_jobs`;

-- إع<PERSON>ة تفعيل فحص Foreign Keys
SET FOREIGN_KEY_CHECKS = 1;

-- رسالة تأكيد
SELECT 'Database reset completed successfully!' as message;
