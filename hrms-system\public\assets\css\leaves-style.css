/* Leave Management Premium Styles */

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 20px;
    padding: 25px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
    transition: all 0.3s ease;
}

.stats-card:hover::before {
    top: -30%;
    right: -30%;
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

/* Leave Type Badges */
.leave-type-badge {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.leave-type-annual {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.leave-type-sick {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.leave-type-emergency {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.leave-type-maternity {
    background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
    color: white;
}

.leave-type-paternity {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.leave-type-unpaid {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-approved {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-rejected {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Form Enhancements */
.form-control-premium {
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 16px 20px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-control-premium:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

/* File Upload Styling */
.file-upload-area {
    border: 2px dashed #e2e8f0;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.file-upload-area.dragover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.02);
}

/* Leave Calendar */
.leave-calendar {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow: hidden;
}

.calendar-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e2e8f0;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.calendar-day:hover {
    background: rgba(102, 126, 234, 0.1);
}

.calendar-day.has-leave {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    font-weight: 600;
}

.calendar-day.has-leave::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background: #10b981;
    border-radius: 50%;
}

/* Leave Balance Cards */
.balance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

.balance-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
    position: relative;
    z-index: 2;
}

.balance-label {
    font-size: 14px;
    opacity: 0.9;
    position: relative;
    z-index: 2;
}

/* Timeline Styles */
.leave-timeline {
    position: relative;
    padding: 20px 0;
}

.timeline-item {
    position: relative;
    padding: 20px 0 20px 60px;
    border-left: 2px solid #e2e8f0;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 25px;
    width: 14px;
    height: 14px;
    background: #667eea;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.timeline-item:last-child {
    border-left: none;
}

.timeline-content {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    position: relative;
}

.timeline-content::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 20px;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-card {
        padding: 20px;
        margin-bottom: 15px;
    }
    
    .balance-number {
        font-size: 2rem;
    }
    
    .timeline-item {
        padding-left: 40px;
    }
    
    .form-control-premium {
        padding: 12px 16px;
    }
}

/* Dark Mode Support */
[data-theme="dark"] .stats-card {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(30, 41, 59, 0.7) 100%);
    color: #e2e8f0;
}

[data-theme="dark"] .leave-calendar {
    background: #1e293b;
}

[data-theme="dark"] .calendar-day {
    border-color: #374151;
    color: #e2e8f0;
}

[data-theme="dark"] .timeline-content {
    background: #1e293b;
    color: #e2e8f0;
}

/* Animation Keyframes */
@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Utility Classes */
.animate-slide-up {
    animation: slideInUp 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
