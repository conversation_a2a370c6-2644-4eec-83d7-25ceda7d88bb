@extends('layouts.app')

@section('title', 'تفاصيل طلب الإجازة')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4 animate-fade-in-down">
    <div>
        <h1 class="h3 mb-2 gradient-text fw-bold">
            <i class="bi bi-eye me-2"></i>
            تفاصيل طلب الإجازة
        </h1>
        <p class="text-muted mb-0">عرض تفاصيل طلب الإجازة والموافقات</p>
    </div>
    <div class="btn-group">
        <a href="{{ route('leaves.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للقائمة
        </a>
        @if($leave->status === 'pending')
            <a href="{{ route('leaves.edit', $leave) }}" class="btn btn-outline-primary">
                <i class="bi bi-pencil me-2"></i>
                تعديل
            </a>
        @endif
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- معلومات الطلب الأساسية -->
        <div class="premium-card mb-4 animate-fade-in-up">
            <div class="card-header bg-transparent border-0 py-4">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-bold">
                        <i class="bi bi-info-circle me-2 text-primary"></i>
                        معلومات الطلب
                    </h5>
                    <span class="badge badge-premium fs-6 bg-{{ $leave->status === 'approved' ? 'success' : ($leave->status === 'rejected' ? 'danger' : 'warning') }}">
                        @switch($leave->status)
                            @case('pending')
                                <i class="bi bi-clock me-1"></i>معلق
                                @break
                            @case('approved')
                                <i class="bi bi-check-circle me-1"></i>موافق عليه
                                @break
                            @case('rejected')
                                <i class="bi bi-x-circle me-1"></i>مرفوض
                                @break
                            @default
                                {{ $leave->status }}
                        @endswitch
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- معلومات الموظف -->
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-person-fill me-2 text-primary"></i>
                                الموظف
                            </label>
                            <div class="info-value">
                                <div class="d-flex align-items-center">
                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 45px; height: 45px;">
                                        <i class="bi bi-person text-white"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ $leave->employee->full_name }}</div>
                                        <small class="text-muted">{{ $leave->employee->employee_id }}</small>
                                        <small class="text-muted d-block">{{ $leave->employee->department->name_ar ?? 'غير محدد' }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نوع الإجازة -->
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-tag me-2 text-info"></i>
                                نوع الإجازة
                            </label>
                            <div class="info-value">
                                <span class="badge bg-light text-dark fs-6">
                                    @switch($leave->type)
                                        @case('annual')
                                            <i class="bi bi-calendar-heart me-1"></i>إجازة سنوية
                                            @break
                                        @case('sick')
                                            <i class="bi bi-heart-pulse me-1"></i>إجازة مرضية
                                            @break
                                        @case('emergency')
                                            <i class="bi bi-exclamation-triangle me-1"></i>إجازة طارئة
                                            @break
                                        @case('maternity')
                                            <i class="bi bi-person-hearts me-1"></i>إجازة أمومة
                                            @break
                                        @case('paternity')
                                            <i class="bi bi-person-check me-1"></i>إجازة أبوة
                                            @break
                                        @case('unpaid')
                                            <i class="bi bi-cash-stack me-1"></i>إجازة بدون راتب
                                            @break
                                        @default
                                            {{ $leave->type }}
                                    @endswitch
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- تاريخ البداية -->
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-calendar-event me-2 text-success"></i>
                                تاريخ البداية
                            </label>
                            <div class="info-value">
                                <span class="badge bg-success fs-6">{{ $leave->start_date->format('Y/m/d') }}</span>
                                <small class="text-muted d-block">{{ $leave->start_date->format('l') }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- تاريخ النهاية -->
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-calendar-x me-2 text-danger"></i>
                                تاريخ النهاية
                            </label>
                            <div class="info-value">
                                <span class="badge bg-danger fs-6">{{ $leave->end_date->format('Y/m/d') }}</span>
                                <small class="text-muted d-block">{{ $leave->end_date->format('l') }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- عدد الأيام -->
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-calendar-range me-2 text-warning"></i>
                                عدد الأيام المطلوبة
                            </label>
                            <div class="info-value">
                                <span class="badge bg-warning text-dark fs-6">
                                    {{ $leave->days_requested }} 
                                    {{ $leave->days_requested == 1 ? 'يوم' : 'أيام' }}
                                </span>
                                @if($leave->is_half_day)
                                    <small class="text-muted d-block">
                                        نصف يوم - {{ $leave->half_day_period === 'morning' ? 'الفترة الصباحية' : 'الفترة المسائية' }}
                                    </small>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- تاريخ الطلب -->
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-clock-history me-2 text-muted"></i>
                                تاريخ تقديم الطلب
                            </label>
                            <div class="info-value">
                                <small class="text-muted">
                                    {{ $leave->created_at->format('Y/m/d H:i') }}
                                    <span class="d-block">{{ $leave->created_at->diffForHumans() }}</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- سبب الإجازة -->
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-chat-text me-2 text-secondary"></i>
                                سبب الإجازة
                            </label>
                            <div class="info-value">
                                <div class="alert alert-light border">
                                    {{ $leave->reason }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المرفق -->
                @if($leave->attachment)
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-paperclip me-2 text-info"></i>
                                المرفق
                            </label>
                            <div class="info-value">
                                <a href="{{ Storage::url($leave->attachment) }}" 
                                   target="_blank" 
                                   class="btn btn-outline-info btn-sm">
                                    <i class="bi bi-download me-2"></i>
                                    تحميل المرفق
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- معلومات الموافقة -->
        @if($leave->status !== 'pending')
        <div class="premium-card mb-4 animate-fade-in-up" style="animation-delay: 0.2s;">
            <div class="card-header bg-transparent border-0 py-4">
                <h5 class="mb-0 fw-bold">
                    <i class="bi bi-clipboard-check me-2 text-success"></i>
                    معلومات الموافقة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- المعتمد من قبل -->
                    @if($leave->approved_by)
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-person-check me-2 text-success"></i>
                                معتمد من قبل
                            </label>
                            <div class="info-value">
                                <div class="fw-bold">{{ $leave->approvedBy->name ?? 'غير محدد' }}</div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- تاريخ الموافقة -->
                    @if($leave->approved_at)
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-calendar-check me-2 text-info"></i>
                                تاريخ الموافقة
                            </label>
                            <div class="info-value">
                                <small class="text-muted">
                                    {{ $leave->approved_at->format('Y/m/d H:i') }}
                                </small>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- ملاحظات الموافقة -->
                    @if($leave->approval_notes)
                    <div class="col-12">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-chat-square-text me-2 text-warning"></i>
                                ملاحظات الموافقة
                            </label>
                            <div class="info-value">
                                <div class="alert alert-{{ $leave->status === 'approved' ? 'success' : 'danger' }} border">
                                    {{ $leave->approval_notes }}
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- الشريط الجانبي -->
    <div class="col-lg-4">
        <!-- إجراءات سريعة -->
        @if($leave->status === 'pending' && auth()->user()->can('approve-leaves'))
        <div class="premium-card mb-4 animate-fade-in-up" style="animation-delay: 0.3s;">
            <div class="card-header bg-transparent border-0 py-4">
                <h6 class="mb-0 fw-bold">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" onclick="approveLeave({{ $leave->id }})">
                        <i class="bi bi-check-circle me-2"></i>
                        موافقة على الطلب
                    </button>
                    <button type="button" class="btn btn-danger" onclick="rejectLeave({{ $leave->id }})">
                        <i class="bi bi-x-circle me-2"></i>
                        رفض الطلب
                    </button>
                </div>
            </div>
        </div>
        @endif

        <!-- إحصائيات الموظف -->
        <div class="premium-card mb-4 animate-fade-in-up" style="animation-delay: 0.4s;">
            <div class="card-header bg-transparent border-0 py-4">
                <h6 class="mb-0 fw-bold">
                    <i class="bi bi-graph-up me-2"></i>
                    إحصائيات الموظف
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="stat-item">
                            <div class="stat-number text-success">{{ $employeeStats['approved'] ?? 0 }}</div>
                            <div class="stat-label">إجازات موافق عليها</div>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="stat-item">
                            <div class="stat-number text-warning">{{ $employeeStats['pending'] ?? 0 }}</div>
                            <div class="stat-label">إجازات معلقة</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number text-danger">{{ $employeeStats['rejected'] ?? 0 }}</div>
                            <div class="stat-label">إجازات مرفوضة</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-number text-info">{{ $employeeStats['total_days'] ?? 0 }}</div>
                            <div class="stat-label">إجمالي الأيام</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- رصيد الإجازات -->
        <div class="premium-card animate-fade-in-up" style="animation-delay: 0.5s;">
            <div class="card-header bg-transparent border-0 py-4">
                <h6 class="mb-0 fw-bold">
                    <i class="bi bi-wallet me-2"></i>
                    رصيد الإجازات
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <div class="stat-item bg-primary text-white">
                            <div class="stat-number">{{ $leaveBalance['annual'] ?? 21 }}</div>
                            <div class="stat-label">الإجازة السنوية المتبقية</div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="stat-item bg-success text-white">
                            <div class="stat-number">{{ $leaveBalance['sick'] ?? 30 }}</div>
                            <div class="stat-label">الإجازة المرضية المتبقية</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Approve Leave Function
function approveLeave(leaveId) {
    Swal.fire({
        title: 'موافقة على الطلب',
        text: 'هل أنت متأكد من الموافقة على هذا الطلب؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، وافق',
        cancelButtonText: 'إلغاء',
        input: 'textarea',
        inputPlaceholder: 'ملاحظات الموافقة (اختياري)...',
        inputAttributes: {
            'aria-label': 'ملاحظات الموافقة'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Send approval request
            fetch(`/leaves/${leaveId}/approve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    approval_notes: result.value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم!', data.message, 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ!', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.fire('خطأ!', 'حدث خطأ في الشبكة', 'error');
            });
        }
    });
}

// Reject Leave Function
function rejectLeave(leaveId) {
    Swal.fire({
        title: 'رفض الطلب',
        text: 'هل أنت متأكد من رفض هذا الطلب؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، ارفض',
        cancelButtonText: 'إلغاء',
        input: 'textarea',
        inputPlaceholder: 'سبب الرفض (مطلوب)...',
        inputAttributes: {
            'aria-label': 'سبب الرفض'
        },
        inputValidator: (value) => {
            if (!value) {
                return 'يرجى كتابة سبب الرفض'
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Send rejection request
            fetch(`/leaves/${leaveId}/reject`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    approval_notes: result.value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم!', data.message, 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ!', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.fire('خطأ!', 'حدث خطأ في الشبكة', 'error');
            });
        }
    });
}
</script>
@endpush

@push('styles')
<style>
.premium-card {
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
    border-radius: 15px;
}

.info-group {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 12px;
    height: 100%;
    transition: all 0.3s ease;
}

.info-group:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.info-label {
    font-size: 14px;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 10px;
    display: block;
}

.info-value {
    font-size: 16px;
    color: #495057;
    font-weight: 500;
}

.stat-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.badge-premium {
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 14px;
}
</style>
@endpush
