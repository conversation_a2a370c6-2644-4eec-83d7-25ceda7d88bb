@extends('layouts.app')

@section('title', 'إدارة الموظفين')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4 animate-fade-in-down">
    <div>
        <h1 class="h3 mb-2 gradient-text fw-bold">
            <i class="bi bi-people me-2"></i>
            إدارة الموظفين
        </h1>
        <p class="text-muted mb-0">إدارة بيانات الموظفين والأقسام</p>
    </div>
    <div class="btn-group">
        <a href="{{ route('employees.create') }}" class="btn btn-premium">
            <i class="bi bi-person-plus me-2"></i>
            إضافة موظف جديد
        </a>
        <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#importModal">
            <i class="bi bi-upload me-2"></i>
            استيراد موظفين
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.1s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-primary mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        إجمالي الموظفين
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark">
                        {{ $employees->count() }}
                    </div>
                </div>
                <div class="bg-primary bg-gradient rounded-circle p-3 animate-float">
                    <i class="bi bi-people-fill text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.2s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-success mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        الموظفين النشطين
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark">
                        {{ $employees->where('employment_status', 'active')->count() }}
                    </div>
                </div>
                <div class="bg-success bg-gradient rounded-circle p-3 animate-float" style="animation-delay: 0.5s;">
                    <i class="bi bi-person-check-fill text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            الموظفين الجدد
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ $employees->where('hire_date', '>=', now()->startOfMonth())->count() }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-plus fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            متوسط الراتب
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ number_format($employees->avg('salary'), 0) }} ر.س
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-cash-stack fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Employees Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="bi bi-table me-2"></i>
            قائمة الموظفين
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered data-table" id="employeesTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>رقم الموظف</th>
                        <th>الاسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>القسم</th>
                        <th>المنصب</th>
                        <th>تاريخ التوظيف</th>
                        <th>الراتب</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($employees as $employee)
                    <tr>
                        <td>{{ $employee->employee_id }}</td>
                        <td>
                            <div class="fw-bold">{{ $employee->full_name }}</div>
                            @if($employee->full_name_ar)
                                <small class="text-muted">{{ $employee->full_name_ar }}</small>
                            @endif
                        </td>
                        <td>{{ $employee->email }}</td>
                        <td>
                            @if($employee->department)
                                <span class="badge bg-info">
                                    {{ $employee->department->name_ar ?? $employee->department->name }}
                                </span>
                            @else
                                <span class="text-muted">غير محدد</span>
                            @endif
                        </td>
                        <td>{{ $employee->position_ar ?? $employee->position }}</td>
                        <td>{{ $employee->hire_date->format('Y/m/d') }}</td>
                        <td>{{ number_format($employee->salary, 2) }} ر.س</td>
                        <td>
                            @switch($employee->employment_status)
                                @case('active')
                                    <span class="badge bg-success">نشط</span>
                                    @break
                                @case('inactive')
                                    <span class="badge bg-warning">غير نشط</span>
                                    @break
                                @case('terminated')
                                    <span class="badge bg-danger">منتهي الخدمة</span>
                                    @break
                                @default
                                    <span class="badge bg-secondary">غير محدد</span>
                            @endswitch
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('employees.show', $employee) }}" 
                                   class="btn btn-sm btn-outline-info" 
                                   title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ route('employees.edit', $employee) }}" 
                                   class="btn btn-sm btn-outline-primary" 
                                   title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button type="button" 
                                        class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteEmployee({{ $employee->id }})"
                                        title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Delete Employee Function
function deleteEmployee(employeeId) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: "لن تتمكن من التراجع عن هذا الإجراء!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/employees/${employeeId}`;
            
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            
            const methodField = document.createElement('input');
            methodField.type = 'hidden';
            methodField.name = '_method';
            methodField.value = 'DELETE';
            
            form.appendChild(csrfToken);
            form.appendChild(methodField);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

// Initialize DataTable
$(document).ready(function() {
    $('#employeesTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        order: [[0, 'asc']]
    });
});
</script>
@endpush

@push('styles')
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    margin: 0 1px;
}

.badge {
    font-size: 0.75em;
}
</style>
@endpush
