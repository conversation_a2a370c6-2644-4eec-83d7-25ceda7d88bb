-- إنشاء قاعدة بيانات نظام إدارة الموارد البشرية
-- Human Resources Management System Database

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS `hrms_system` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE `hrms_system`;

-- إنشاء مستخدم مخصص للنظام (اختياري)
-- CREATE USER IF NOT EXISTS 'hrms_user'@'localhost' IDENTIFIED BY 'hrms_password_2025';
-- GRANT ALL PRIVILEGES ON hrms_system.* TO 'hrms_user'@'localhost';
-- FLUSH PRIVILEGES;

-- تعليق: سيتم إنشاء الجداول تلقائياً عبر Laravel Migrations
-- يمكنك تشغيل هذا الملف في phpMyAdmin أو MySQL Command Line

-- معلومات الاتصال:
-- Host: 127.0.0.1 (localhost)
-- Port: 3306
-- Database: hrms_system
-- Username: root
-- Password: (فارغ في XAMPP الافتراضي)

-- للتحقق من إنشاء قاعدة البيانات:
-- SHOW DATABASES LIKE 'hrms_system';

-- للتحقق من الترميز:
-- SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME 
-- FROM information_schema.SCHEMATA 
-- WHERE SCHEMA_NAME = 'hrms_system';
