@echo off
echo ========================================
echo    إصلاح مشكلة الهجرات - Fix Migrations
echo ========================================
echo.

echo 1. حذف جدول migrations...
echo    Dropping migrations table...
mysql -u root -h 127.0.0.1 -P 3306 hrms_system -e "DROP TABLE IF EXISTS migrations;"

echo.
echo 2. مسح الكاش...
echo    Clearing cache...
php artisan config:clear
php artisan cache:clear

echo.
echo 3. تشغيل الهجرات بالترتيب الصحيح...
echo    Running migrations in correct order...
php artisan migrate --force

echo.
echo 4. إضافة البيانات التجريبية...
echo    Seeding database...
php artisan db:seed --force

echo.
echo ========================================
echo تم إصلاح المشكلة بنجاح!
echo Problem fixed successfully!
echo ========================================
echo.
echo يمكنك الآن تشغيل النظام:
echo You can now run the system:
echo php artisan serve
echo.
pause
