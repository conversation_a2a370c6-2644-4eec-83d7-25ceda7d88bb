@echo off
echo ========================================
echo    تهيئة قاعدة بيانات MySQL للنظام
echo    HRMS MySQL Database Setup
echo ========================================
echo.

echo 1. التحقق من تشغيل XAMPP...
echo    Checking XAMPP services...
echo.

echo 2. إنشاء قاعدة البيانات...
echo    Creating database...
echo.
echo يرجى تشغيل الأمر التالي في phpMyAdmin أو MySQL Command Line:
echo Please run the following in phpMyAdmin or MySQL Command Line:
echo.
echo CREATE DATABASE IF NOT EXISTS hrms_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
echo.

echo 3. تحديث إعدادات Laravel...
echo    Updating Laravel configuration...
echo.

echo 4. مسح الكاش...
echo    Clearing cache...
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

echo.
echo 5. تشغيل الهجرات...
echo    Running migrations...
php artisan migrate

echo.
echo 6. إضافة البيانات التجريبية...
echo    Seeding database...
php artisan db:seed

echo.
echo 7. إنشاء رابط التخزين...
echo    Creating storage link...
php artisan storage:link

echo.
echo ========================================
echo تم الانتهاء من تهيئة قاعدة البيانات!
echo Database setup completed!
echo ========================================
echo.
echo يمكنك الآن تشغيل النظام باستخدام:
echo You can now run the system using:
echo php artisan serve
echo.
echo ثم افتح المتصفح على:
echo Then open browser at:
echo http://localhost:8000
echo.
echo بيانات الدخول:
echo Login credentials:
echo Admin: <EMAIL> / 123456
echo HR: <EMAIL> / 123456
echo.
pause
