@extends('layouts.app')

@section('title', 'التقارير والإحصائيات')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4 animate-fade-in-down">
    <div>
        <h1 class="h3 mb-2 gradient-text fw-bold">
            <i class="bi bi-graph-up me-2"></i>
            التقارير والإحصائيات
        </h1>
        <p class="text-muted mb-0">تقارير شاملة وإحصائيات متقدمة للنظام</p>
    </div>
    <div class="btn-group">
        <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
            <i class="bi bi-download me-2"></i>
            تصدير التقارير
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="exportReport('all', 'excel')">
                <i class="bi bi-file-excel me-2"></i>Excel
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="exportReport('all', 'pdf')">
                <i class="bi bi-file-pdf me-2"></i>PDF
            </a></li>
        </ul>
    </div>
</div>

<!-- Statistics Overview -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.1s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-primary mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        إجمالي الموظفين
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark">
                        {{ $stats['total_employees'] }}
                    </div>
                </div>
                <div class="bg-primary bg-gradient rounded-circle p-3 animate-float">
                    <i class="bi bi-people-fill text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.2s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-success mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        حاضر اليوم
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark">
                        {{ $stats['present_today'] }}
                    </div>
                </div>
                <div class="bg-success bg-gradient rounded-circle p-3 animate-float" style="animation-delay: 0.5s;">
                    <i class="bi bi-check-circle-fill text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.3s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-warning mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        إجازات معلقة
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark">
                        {{ $stats['pending_leaves'] }}
                    </div>
                </div>
                <div class="bg-warning bg-gradient rounded-circle p-3 animate-float" style="animation-delay: 1s;">
                    <i class="bi bi-calendar-x text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.4s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-info mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        مرتبات الشهر
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark">
                        {{ number_format($stats['monthly_payroll'], 0) }}
                    </div>
                    <div class="text-muted small mt-1">ر.س</div>
                </div>
                <div class="bg-info bg-gradient rounded-circle p-3 animate-float" style="animation-delay: 1.5s;">
                    <i class="bi bi-cash-stack text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Categories -->
<div class="row mb-4">
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="premium-card report-card animate-fade-in-up" style="animation-delay: 0.5s;">
            <div class="card-body text-center p-4">
                <div class="report-icon mb-3">
                    <i class="bi bi-people-fill text-primary" style="font-size: 3rem;"></i>
                </div>
                <h5 class="card-title fw-bold mb-3">تقارير الموظفين</h5>
                <p class="card-text text-muted mb-4">تقارير شاملة عن بيانات الموظفين والأقسام</p>
                <a href="{{ route('reports.employees') }}" class="btn btn-outline-primary">
                    <i class="bi bi-eye me-2"></i>
                    عرض التقرير
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
        <div class="premium-card report-card animate-fade-in-up" style="animation-delay: 0.6s;">
            <div class="card-body text-center p-4">
                <div class="report-icon mb-3">
                    <i class="bi bi-clock-fill text-success" style="font-size: 3rem;"></i>
                </div>
                <h5 class="card-title fw-bold mb-3">تقارير الحضور</h5>
                <p class="card-text text-muted mb-4">تقارير الحضور والانصراف والساعات الإضافية</p>
                <a href="{{ route('reports.attendance') }}" class="btn btn-outline-success">
                    <i class="bi bi-eye me-2"></i>
                    عرض التقرير
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
        <div class="premium-card report-card animate-fade-in-up" style="animation-delay: 0.7s;">
            <div class="card-body text-center p-4">
                <div class="report-icon mb-3">
                    <i class="bi bi-calendar-check text-warning" style="font-size: 3rem;"></i>
                </div>
                <h5 class="card-title fw-bold mb-3">تقارير الإجازات</h5>
                <p class="card-text text-muted mb-4">تقارير طلبات الإجازات ورصيد الموظفين</p>
                <a href="{{ route('reports.leaves') }}" class="btn btn-outline-warning">
                    <i class="bi bi-eye me-2"></i>
                    عرض التقرير
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
        <div class="premium-card report-card animate-fade-in-up" style="animation-delay: 0.8s;">
            <div class="card-body text-center p-4">
                <div class="report-icon mb-3">
                    <i class="bi bi-cash-stack text-info" style="font-size: 3rem;"></i>
                </div>
                <h5 class="card-title fw-bold mb-3">تقارير المرتبات</h5>
                <p class="card-text text-muted mb-4">تقارير المرتبات الشهرية والسنوية</p>
                <a href="{{ route('reports.payroll') }}" class="btn btn-outline-info">
                    <i class="bi bi-eye me-2"></i>
                    عرض التقرير
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
        <div class="premium-card report-card animate-fade-in-up" style="animation-delay: 0.9s;">
            <div class="card-body text-center p-4">
                <div class="report-icon mb-3">
                    <i class="bi bi-building text-danger" style="font-size: 3rem;"></i>
                </div>
                <h5 class="card-title fw-bold mb-3">تقارير الأقسام</h5>
                <p class="card-text text-muted mb-4">إحصائيات الأقسام والموظفين</p>
                <a href="{{ route('reports.departments') }}" class="btn btn-outline-danger">
                    <i class="bi bi-eye me-2"></i>
                    عرض التقرير
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-4 col-md-6 mb-4">
        <div class="premium-card report-card animate-fade-in-up" style="animation-delay: 1.0s;">
            <div class="card-body text-center p-4">
                <div class="report-icon mb-3">
                    <i class="bi bi-graph-up text-secondary" style="font-size: 3rem;"></i>
                </div>
                <h5 class="card-title fw-bold mb-3">تقارير مخصصة</h5>
                <p class="card-text text-muted mb-4">إنشاء تقارير مخصصة حسب الحاجة</p>
                <button class="btn btn-outline-secondary" onclick="createCustomReport()">
                    <i class="bi bi-plus-circle me-2"></i>
                    إنشاء تقرير
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="premium-card animate-fade-in-up" style="animation-delay: 1.1s;">
            <div class="card-header bg-transparent border-0 py-4">
                <h5 class="mb-0 fw-bold">
                    <i class="bi bi-bar-chart me-2 text-primary"></i>
                    توزيع الموظفين حسب الأقسام
                </h5>
            </div>
            <div class="card-body">
                <canvas id="departmentChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="premium-card animate-fade-in-up" style="animation-delay: 1.2s;">
            <div class="card-header bg-transparent border-0 py-4">
                <h5 class="mb-0 fw-bold">
                    <i class="bi bi-line-chart me-2 text-success"></i>
                    اتجاهات الحضور الشهرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="attendanceChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12 mb-4">
        <div class="premium-card animate-fade-in-up" style="animation-delay: 1.3s;">
            <div class="card-header bg-transparent border-0 py-4">
                <h5 class="mb-0 fw-bold">
                    <i class="bi bi-pie-chart me-2 text-warning"></i>
                    إحصائيات الإجازات حسب النوع
                </h5>
            </div>
            <div class="card-body">
                <canvas id="leaveChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Department Chart
    const departmentCtx = document.getElementById('departmentChart').getContext('2d');
    new Chart(departmentCtx, {
        type: 'doughnut',
        data: {
            labels: [
                @foreach($departmentStats as $dept)
                    '{{ $dept->name }}',
                @endforeach
            ],
            datasets: [{
                data: [
                    @foreach($departmentStats as $dept)
                        {{ $dept->employees_count }},
                    @endforeach
                ],
                backgroundColor: [
                    '#667eea', '#764ba2', '#f093fb', '#f5576c',
                    '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Attendance Chart
    const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
    new Chart(attendanceCtx, {
        type: 'line',
        data: {
            labels: [
                @foreach($attendanceTrends as $trend)
                    'الشهر {{ $trend->month }}',
                @endforeach
            ],
            datasets: [{
                label: 'حاضر',
                data: [
                    @foreach($attendanceTrends as $trend)
                        {{ $trend->present }},
                    @endforeach
                ],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }, {
                label: 'غائب',
                data: [
                    @foreach($attendanceTrends as $trend)
                        {{ $trend->absent }},
                    @endforeach
                ],
                borderColor: '#dc3545',
                backgroundColor: 'rgba(220, 53, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Leave Chart
    const leaveCtx = document.getElementById('leaveChart').getContext('2d');
    new Chart(leaveCtx, {
        type: 'bar',
        data: {
            labels: [
                @foreach($leaveStats as $stat)
                    '{{ $stat->type }}',
                @endforeach
            ],
            datasets: [{
                label: 'عدد الإجازات',
                data: [
                    @foreach($leaveStats as $stat)
                        {{ $stat->count }},
                    @endforeach
                ],
                backgroundColor: [
                    '#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});

// Export Report Function
function exportReport(type, format) {
    fetch('{{ route("reports.export") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            type: type,
            format: format
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire('تم!', data.message, 'success');
        } else {
            Swal.fire('خطأ!', data.message, 'error');
        }
    })
    .catch(error => {
        Swal.fire('خطأ!', 'حدث خطأ في الشبكة', 'error');
    });
}

// Create Custom Report Function
function createCustomReport() {
    Swal.fire({
        title: 'إنشاء تقرير مخصص',
        text: 'هذه الميزة ستكون متاحة قريباً',
        icon: 'info',
        confirmButtonText: 'موافق'
    });
}
</script>
@endpush

@push('styles')
<style>
.report-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.report-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.report-icon {
    transition: all 0.3s ease;
}

.report-card:hover .report-icon {
    transform: scale(1.1);
}

.premium-card {
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
    border-radius: 15px;
}

.stats-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}
</style>
@endpush
