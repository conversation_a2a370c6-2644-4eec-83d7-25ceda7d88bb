<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\Attendance;
use App\Models\Leave;
use App\Models\Payroll;
use App\Models\Department;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ReportController extends Controller
{
    /**
     * Display the reports dashboard
     */
    public function index()
    {
        // Get current month statistics
        $currentMonth = Carbon::now();
        
        $stats = [
            'total_employees' => Employee::where('employment_status', 'active')->count(),
            'present_today' => Attendance::where('date', today())
                                        ->where('status', 'present')
                                        ->count(),
            'pending_leaves' => Leave::where('status', 'pending')->count(),
            'monthly_payroll' => Payroll::where('month', $currentMonth->month)
                                      ->where('year', $currentMonth->year)
                                      ->where('status', 'approved')
                                      ->sum('net_salary')
        ];
        
        // Get department-wise employee count
        $departmentStats = Department::withCount('employees')->get();
        
        // Get monthly attendance trends
        $attendanceTrends = Attendance::select(
                DB::raw('MONTH(date) as month'),
                DB::raw('COUNT(CASE WHEN status = "present" THEN 1 END) as present'),
                DB::raw('COUNT(CASE WHEN status = "absent" THEN 1 END) as absent')
            )
            ->whereYear('date', $currentMonth->year)
            ->groupBy(DB::raw('MONTH(date)'))
            ->orderBy('month')
            ->get();
        
        // Get leave statistics by type
        $leaveStats = Leave::select('type', DB::raw('COUNT(*) as count'))
                          ->where('status', 'approved')
                          ->whereYear('start_date', $currentMonth->year)
                          ->groupBy('type')
                          ->get();
        
        return view('reports.index', compact(
            'stats', 
            'departmentStats', 
            'attendanceTrends', 
            'leaveStats'
        ));
    }

    /**
     * Employee Report
     */
    public function employees(Request $request)
    {
        $query = Employee::with(['department']);
        
        // Apply filters
        if ($request->filled('department_id')) {
            $query->where('department_id', $request->department_id);
        }
        
        if ($request->filled('employment_status')) {
            $query->where('employment_status', $request->employment_status);
        }
        
        if ($request->filled('date_from')) {
            $query->where('hire_date', '>=', $request->date_from);
        }
        
        if ($request->filled('date_to')) {
            $query->where('hire_date', '<=', $request->date_to);
        }
        
        $employees = $query->orderBy('first_name')->get();
        $departments = Department::orderBy('name')->get();
        
        return view('reports.employees', compact('employees', 'departments'));
    }

    /**
     * Attendance Report
     */
    public function attendance(Request $request)
    {
        $dateFrom = $request->input('date_from', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $dateTo = $request->input('date_to', Carbon::now()->endOfMonth()->format('Y-m-d'));
        
        $query = Attendance::with(['employee.department'])
                          ->whereBetween('date', [$dateFrom, $dateTo]);
        
        // Apply filters
        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }
        
        if ($request->filled('department_id')) {
            $query->whereHas('employee', function($q) use ($request) {
                $q->where('department_id', $request->department_id);
            });
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        $attendances = $query->orderBy('date', 'desc')->get();
        
        // Get summary statistics
        $summary = [
            'total_records' => $attendances->count(),
            'present_count' => $attendances->where('status', 'present')->count(),
            'absent_count' => $attendances->where('status', 'absent')->count(),
            'late_count' => $attendances->where('status', 'late')->count(),
            'total_hours' => $attendances->sum('total_hours')
        ];
        
        $employees = Employee::where('employment_status', 'active')->orderBy('first_name')->get();
        $departments = Department::orderBy('name')->get();
        
        return view('reports.attendance', compact(
            'attendances', 
            'summary', 
            'employees', 
            'departments',
            'dateFrom',
            'dateTo'
        ));
    }

    /**
     * Leave Report
     */
    public function leaves(Request $request)
    {
        $dateFrom = $request->input('date_from', Carbon::now()->startOfYear()->format('Y-m-d'));
        $dateTo = $request->input('date_to', Carbon::now()->endOfYear()->format('Y-m-d'));
        
        $query = Leave::with(['employee.department'])
                     ->whereBetween('start_date', [$dateFrom, $dateTo]);
        
        // Apply filters
        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }
        
        if ($request->filled('department_id')) {
            $query->whereHas('employee', function($q) use ($request) {
                $q->where('department_id', $request->department_id);
            });
        }
        
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        $leaves = $query->orderBy('start_date', 'desc')->get();
        
        // Get summary statistics
        $summary = [
            'total_requests' => $leaves->count(),
            'approved_count' => $leaves->where('status', 'approved')->count(),
            'pending_count' => $leaves->where('status', 'pending')->count(),
            'rejected_count' => $leaves->where('status', 'rejected')->count(),
            'total_days' => $leaves->where('status', 'approved')->sum('days_requested')
        ];
        
        $employees = Employee::where('employment_status', 'active')->orderBy('first_name')->get();
        $departments = Department::orderBy('name')->get();
        
        return view('reports.leaves', compact(
            'leaves', 
            'summary', 
            'employees', 
            'departments',
            'dateFrom',
            'dateTo'
        ));
    }

    /**
     * Payroll Report
     */
    public function payroll(Request $request)
    {
        $month = $request->input('month', Carbon::now()->month);
        $year = $request->input('year', Carbon::now()->year);
        
        $query = Payroll::with(['employee.department'])
                       ->where('month', $month)
                       ->where('year', $year);
        
        // Apply filters
        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }
        
        if ($request->filled('department_id')) {
            $query->whereHas('employee', function($q) use ($request) {
                $q->where('department_id', $request->department_id);
            });
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        $payrolls = $query->orderBy('net_salary', 'desc')->get();
        
        // Get summary statistics
        $summary = [
            'total_payrolls' => $payrolls->count(),
            'approved_count' => $payrolls->where('status', 'approved')->count(),
            'pending_count' => $payrolls->where('status', 'pending')->count(),
            'total_basic_salary' => $payrolls->sum('basic_salary'),
            'total_allowances' => $payrolls->sum('allowances'),
            'total_deductions' => $payrolls->sum('deductions'),
            'total_net_salary' => $payrolls->where('status', 'approved')->sum('net_salary')
        ];
        
        $employees = Employee::where('employment_status', 'active')->orderBy('first_name')->get();
        $departments = Department::orderBy('name')->get();
        
        return view('reports.payroll', compact(
            'payrolls', 
            'summary', 
            'employees', 
            'departments',
            'month',
            'year'
        ));
    }

    /**
     * Department Report
     */
    public function departments()
    {
        $departments = Department::withCount([
            'employees',
            'employees as active_employees_count' => function($query) {
                $query->where('employment_status', 'active');
            }
        ])->get();
        
        // Get additional statistics for each department
        foreach ($departments as $department) {
            // Get attendance rate for current month
            $currentMonth = Carbon::now();
            $totalWorkingDays = $currentMonth->daysInMonth;
            
            $attendanceStats = Attendance::whereHas('employee', function($q) use ($department) {
                    $q->where('department_id', $department->id);
                })
                ->whereMonth('date', $currentMonth->month)
                ->whereYear('date', $currentMonth->year)
                ->selectRaw('
                    COUNT(CASE WHEN status = "present" THEN 1 END) as present_count,
                    COUNT(CASE WHEN status = "absent" THEN 1 END) as absent_count
                ')
                ->first();
            
            $department->attendance_rate = $department->active_employees_count > 0 
                ? round(($attendanceStats->present_count / ($department->active_employees_count * $totalWorkingDays)) * 100, 2)
                : 0;
            
            // Get average salary
            $avgSalary = Payroll::whereHas('employee', function($q) use ($department) {
                    $q->where('department_id', $department->id);
                })
                ->where('month', $currentMonth->month)
                ->where('year', $currentMonth->year)
                ->where('status', 'approved')
                ->avg('net_salary');
            
            $department->avg_salary = $avgSalary ?? 0;
        }
        
        return view('reports.departments', compact('departments'));
    }

    /**
     * Export report data
     */
    public function export(Request $request)
    {
        $type = $request->input('type');
        $format = $request->input('format', 'excel');
        
        // This would typically use a package like Laravel Excel
        // For now, return JSON response
        return response()->json([
            'success' => true,
            'message' => "سيتم تصدير تقرير {$type} بصيغة {$format} قريباً"
        ]);
    }
}
