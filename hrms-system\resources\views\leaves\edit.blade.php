@extends('layouts.app')

@section('title', 'تعديل طلب الإجازة')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4 animate-fade-in-down">
    <div>
        <h1 class="h3 mb-2 gradient-text fw-bold">
            <i class="bi bi-pencil-square me-2"></i>
            تعديل طلب الإجازة
        </h1>
        <p class="text-muted mb-0">تحديث بيانات طلب الإجازة</p>
    </div>
    <div class="btn-group">
        <a href="{{ route('leaves.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للقائمة
        </a>
        <a href="{{ route('leaves.show', $leave) }}" class="btn btn-outline-info">
            <i class="bi bi-eye me-2"></i>
            عرض التفاصيل
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="premium-card animate-fade-in-up">
            <div class="card-header bg-transparent border-0 py-4">
                <h5 class="mb-0 fw-bold">
                    <i class="bi bi-calendar-plus me-2 text-primary"></i>
                    تحديث بيانات طلب الإجازة
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('leaves.update', $leave) }}" method="POST" id="leaveForm" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    
                    <div class="row">
                        <!-- اختيار الموظف -->
                        <div class="col-md-6 mb-4">
                            <label for="employee_id" class="form-label fw-semibold">
                                <i class="bi bi-person me-1"></i>
                                الموظف <span class="text-danger">*</span>
                            </label>
                            <select class="form-select select2 @error('employee_id') is-invalid @enderror" 
                                    id="employee_id" 
                                    name="employee_id" 
                                    required>
                                <option value="">اختر الموظف</option>
                                @foreach($employees as $employee)
                                    <option value="{{ $employee->id }}" 
                                            {{ (old('employee_id', $leave->employee_id) == $employee->id) ? 'selected' : '' }}>
                                        {{ $employee->full_name }} ({{ $employee->employee_id }})
                                    </option>
                                @endforeach
                            </select>
                            @error('employee_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- نوع الإجازة -->
                        <div class="col-md-6 mb-4">
                            <label for="type" class="form-label fw-semibold">
                                <i class="bi bi-tag me-1"></i>
                                نوع الإجازة <span class="text-danger">*</span>
                            </label>
                            <select class="form-select @error('type') is-invalid @enderror" 
                                    id="type" 
                                    name="type" 
                                    required>
                                <option value="">اختر نوع الإجازة</option>
                                <option value="annual" {{ old('type', $leave->type) == 'annual' ? 'selected' : '' }}>
                                    إجازة سنوية
                                </option>
                                <option value="sick" {{ old('type', $leave->type) == 'sick' ? 'selected' : '' }}>
                                    إجازة مرضية
                                </option>
                                <option value="emergency" {{ old('type', $leave->type) == 'emergency' ? 'selected' : '' }}>
                                    إجازة طارئة
                                </option>
                                <option value="maternity" {{ old('type', $leave->type) == 'maternity' ? 'selected' : '' }}>
                                    إجازة أمومة
                                </option>
                                <option value="paternity" {{ old('type', $leave->type) == 'paternity' ? 'selected' : '' }}>
                                    إجازة أبوة
                                </option>
                                <option value="unpaid" {{ old('type', $leave->type) == 'unpaid' ? 'selected' : '' }}>
                                    إجازة بدون راتب
                                </option>
                            </select>
                            @error('type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <!-- تاريخ البداية -->
                        <div class="col-md-6 mb-4">
                            <label for="start_date" class="form-label fw-semibold">
                                <i class="bi bi-calendar-event me-1 text-success"></i>
                                تاريخ البداية <span class="text-danger">*</span>
                            </label>
                            <input type="date" 
                                   class="form-control @error('start_date') is-invalid @enderror" 
                                   id="start_date" 
                                   name="start_date" 
                                   value="{{ old('start_date', $leave->start_date->format('Y-m-d')) }}" 
                                   required>
                            @error('start_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- تاريخ النهاية -->
                        <div class="col-md-6 mb-4">
                            <label for="end_date" class="form-label fw-semibold">
                                <i class="bi bi-calendar-x me-1 text-danger"></i>
                                تاريخ النهاية <span class="text-danger">*</span>
                            </label>
                            <input type="date" 
                                   class="form-control @error('end_date') is-invalid @enderror" 
                                   id="end_date" 
                                   name="end_date" 
                                   value="{{ old('end_date', $leave->end_date->format('Y-m-d')) }}" 
                                   required>
                            @error('end_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <!-- عدد الأيام المطلوبة -->
                        <div class="col-md-6 mb-4">
                            <label for="days_requested" class="form-label fw-semibold">
                                <i class="bi bi-calendar-range me-1"></i>
                                عدد الأيام المطلوبة
                            </label>
                            <input type="number" 
                                   class="form-control @error('days_requested') is-invalid @enderror" 
                                   id="days_requested" 
                                   name="days_requested" 
                                   min="1" 
                                   max="365"
                                   value="{{ old('days_requested', $leave->days_requested) }}" 
                                   readonly>
                            @error('days_requested')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">سيتم حساب عدد الأيام تلقائياً</div>
                        </div>

                        <!-- إجازة نصف يوم -->
                        <div class="col-md-6 mb-4">
                            <label class="form-label fw-semibold">
                                <i class="bi bi-clock-split me-1"></i>
                                نوع الإجازة
                            </label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_half_day" name="is_half_day" value="1" 
                                       {{ old('is_half_day', $leave->is_half_day) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_half_day">
                                    إجازة نصف يوم
                                </label>
                            </div>
                            <div id="half_day_period_container" style="{{ old('is_half_day', $leave->is_half_day) ? 'display: block;' : 'display: none;' }}" class="mt-2">
                                <select class="form-select" id="half_day_period" name="half_day_period">
                                    <option value="">اختر الفترة</option>
                                    <option value="morning" {{ old('half_day_period', $leave->half_day_period) == 'morning' ? 'selected' : '' }}>الفترة الصباحية</option>
                                    <option value="afternoon" {{ old('half_day_period', $leave->half_day_period) == 'afternoon' ? 'selected' : '' }}>الفترة المسائية</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- سبب الإجازة -->
                    <div class="mb-4">
                        <label for="reason" class="form-label fw-semibold">
                            <i class="bi bi-chat-text me-1"></i>
                            سبب الإجازة <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('reason') is-invalid @enderror" 
                                  id="reason" 
                                  name="reason" 
                                  rows="4" 
                                  placeholder="اكتب سبب طلب الإجازة بالتفصيل..."
                                  required>{{ old('reason', $leave->reason) }}</textarea>
                        @error('reason')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- رفع مرفق جديد -->
                    <div class="mb-4">
                        <label for="attachment" class="form-label fw-semibold">
                            <i class="bi bi-paperclip me-1"></i>
                            مرفق جديد (اختياري)
                        </label>
                        @if($leave->attachment)
                            <div class="alert alert-info mb-2">
                                <i class="bi bi-info-circle me-2"></i>
                                يوجد مرفق حالي: 
                                <a href="{{ Storage::url($leave->attachment) }}" target="_blank" class="alert-link">
                                    عرض المرفق الحالي
                                </a>
                            </div>
                        @endif
                        <input type="file" 
                               class="form-control @error('attachment') is-invalid @enderror" 
                               id="attachment" 
                               name="attachment"
                               accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                        @error('attachment')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">يمكنك رفع ملف PDF أو Word أو صورة (حد أقصى 5MB). سيتم استبدال المرفق الحالي إن وجد.</div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            تنبيه مهم:
                        </h6>
                        <ul class="mb-0">
                            <li>يمكن تعديل الطلب فقط إذا كان في حالة "معلق"</li>
                            <li>سيتم إعادة مراجعة الطلب بعد التعديل</li>
                            <li>تأكد من صحة جميع البيانات قبل الحفظ</li>
                        </ul>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                            <i class="bi bi-x-circle me-2"></i>
                            إلغاء
                        </button>
                        <div>
                            <button type="reset" class="btn btn-outline-warning me-2">
                                <i class="bi bi-arrow-clockwise me-2"></i>
                                إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-premium">
                                <i class="bi bi-check-circle me-2"></i>
                                حفظ التحديثات
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap-5',
        placeholder: 'اختر الموظف',
        allowClear: true
    });

    // Calculate days automatically
    function calculateDays() {
        const startDate = $('#start_date').val();
        const endDate = $('#end_date').val();
        const isHalfDay = $('#is_half_day').is(':checked');
        
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            
            if (end >= start) {
                const timeDiff = end.getTime() - start.getTime();
                let daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
                
                if (isHalfDay && daysDiff === 1) {
                    daysDiff = 0.5;
                }
                
                $('#days_requested').val(daysDiff);
            }
        }
    }

    // Auto-calculate days when dates change
    $('#start_date, #end_date').on('change', calculateDays);

    // Handle half day checkbox
    $('#is_half_day').on('change', function() {
        if ($(this).is(':checked')) {
            $('#half_day_period_container').show();
            $('#half_day_period').prop('required', true);
        } else {
            $('#half_day_period_container').hide();
            $('#half_day_period').prop('required', false);
        }
        calculateDays();
    });

    // Set minimum end date based on start date
    $('#start_date').on('change', function() {
        const startDate = $(this).val();
        $('#end_date').attr('min', startDate);
        if ($('#end_date').val() < startDate) {
            $('#end_date').val(startDate);
        }
        calculateDays();
    });

    // Form validation
    $('#leaveForm').on('submit', function(e) {
        const employeeId = $('#employee_id').val();
        const type = $('#type').val();
        const startDate = $('#start_date').val();
        const endDate = $('#end_date').val();
        const reason = $('#reason').val().trim();
        
        if (!employeeId || !type || !startDate || !endDate || !reason) {
            e.preventDefault();
            Swal.fire({
                title: 'خطأ في البيانات',
                text: 'يرجى ملء جميع الحقول المطلوبة',
                icon: 'error',
                confirmButtonText: 'موافق'
            });
            return false;
        }
        
        // Show loading
        Swal.fire({
            title: 'جاري حفظ التحديثات...',
            text: 'يرجى الانتظار',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    });

    // Initialize half day display
    if ($('#is_half_day').is(':checked')) {
        $('#half_day_period_container').show();
        $('#half_day_period').prop('required', true);
    }

    // Calculate days on page load
    calculateDays();
});
</script>
@endpush

@push('styles')
<style>
.premium-card {
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
    border-radius: 15px;
}

.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-premium {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 25px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-premium:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.select2-container--bootstrap-5 .select2-selection {
    border-color: #dee2e6;
}

.select2-container--bootstrap-5 .select2-selection:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}
</style>
@endpush
