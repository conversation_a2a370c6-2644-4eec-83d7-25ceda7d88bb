@extends('layouts.app')

@section('title', 'إدارة المرتبات')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4 animate-fade-in-down">
    <div>
        <h1 class="h3 mb-2 gradient-text fw-bold">
            <i class="bi bi-cash-stack me-2"></i>
            إدارة المرتبات
        </h1>
        <p class="text-muted mb-0">إدارة مرتبات الموظفين والموافقات</p>
    </div>
    <div class="btn-group">
        <a href="{{ route('payroll.create') }}" class="btn btn-premium">
            <i class="bi bi-plus-circle me-2"></i>
            إضافة راتب
        </a>
        <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#generateAllModal">
            <i class="bi bi-gear-fill me-2"></i>
            إنشاء مرتبات شهرية
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.1s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-info mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        إجمالي المرتبات
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark">
                        {{ $stats['total_payrolls'] }}
                    </div>
                </div>
                <div class="bg-info bg-gradient rounded-circle p-3 animate-float">
                    <i class="bi bi-cash-stack text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.2s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-warning mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        مرتبات معلقة
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark">
                        {{ $stats['pending_payrolls'] }}
                    </div>
                </div>
                <div class="bg-warning bg-gradient rounded-circle p-3 animate-float" style="animation-delay: 0.5s;">
                    <i class="bi bi-clock-fill text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.3s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-success mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        مرتبات موافق عليها
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark">
                        {{ $stats['approved_payrolls'] }}
                    </div>
                </div>
                <div class="bg-success bg-gradient rounded-circle p-3 animate-float" style="animation-delay: 1s;">
                    <i class="bi bi-check-circle-fill text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.4s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-primary mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        إجمالي المبلغ
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark">
                        {{ number_format($stats['total_amount'], 0) }}
                    </div>
                    <div class="text-muted small mt-1">ر.س</div>
                </div>
                <div class="bg-primary bg-gradient rounded-circle p-3 animate-float" style="animation-delay: 1.5s;">
                    <i class="bi bi-currency-dollar text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="premium-card mb-4 animate-fade-in-up" style="animation-delay: 0.5s;">
    <div class="card-body">
        <form method="GET" action="{{ route('payroll.index') }}" class="row g-3">
            <div class="col-md-3">
                <label for="month_filter" class="form-label fw-semibold">الشهر</label>
                <select class="form-select" id="month_filter" name="month">
                    <option value="">جميع الشهور</option>
                    @for($i = 1; $i <= 12; $i++)
                        <option value="{{ $i }}" {{ request('month') == $i ? 'selected' : '' }}>
                            {{ \Carbon\Carbon::create()->month($i)->translatedFormat('F') }}
                        </option>
                    @endfor
                </select>
            </div>
            <div class="col-md-3">
                <label for="year_filter" class="form-label fw-semibold">السنة</label>
                <select class="form-select" id="year_filter" name="year">
                    <option value="">جميع السنوات</option>
                    @for($year = date('Y'); $year >= 2020; $year--)
                        <option value="{{ $year }}" {{ request('year') == $year ? 'selected' : '' }}>
                            {{ $year }}
                        </option>
                    @endfor
                </select>
            </div>
            <div class="col-md-3">
                <label for="employee_filter" class="form-label fw-semibold">الموظف</label>
                <select class="form-select select2" id="employee_filter" name="employee_id">
                    <option value="">جميع الموظفين</option>
                    @foreach(\App\Models\Employee::where('employment_status', 'active')->get() as $employee)
                        <option value="{{ $employee->id }}" {{ request('employee_id') == $employee->id ? 'selected' : '' }}>
                            {{ $employee->full_name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="bi bi-funnel me-1"></i>
                    تصفية
                </button>
                <a href="{{ route('payroll.index') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-x-circle me-1"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Payroll Table -->
<div class="premium-card animate-fade-in-up" style="animation-delay: 0.6s;">
    <div class="card-header bg-transparent border-0 py-4">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0 fw-bold gradient-text">
                <i class="bi bi-table me-2"></i>
                سجلات المرتبات
            </h5>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-download me-1"></i>
                    تصدير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#"><i class="bi bi-file-excel me-2"></i>Excel</a></li>
                    <li><a class="dropdown-item" href="#"><i class="bi bi-file-pdf me-2"></i>PDF</a></li>
                    <li><a class="dropdown-item" href="#"><i class="bi bi-printer me-2"></i>طباعة</a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card-body pt-0">
        <div class="table-responsive">
            <table class="table table-hover" id="payrollTable">
                <thead class="table-light">
                    <tr>
                        <th>الموظف</th>
                        <th>الشهر/السنة</th>
                        <th>الراتب الأساسي</th>
                        <th>البدلات</th>
                        <th>الخصومات</th>
                        <th>صافي الراتب</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($payrolls as $payroll)
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 35px; height: 35px;">
                                    <i class="bi bi-person text-white"></i>
                                </div>
                                <div>
                                    <div class="fw-semibold">{{ $payroll->employee->full_name }}</div>
                                    <small class="text-muted">{{ $payroll->employee->employee_id }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark">
                                {{ \Carbon\Carbon::create()->month($payroll->month)->translatedFormat('F') }} {{ $payroll->year }}
                            </span>
                        </td>
                        <td>{{ number_format($payroll->basic_salary, 2) }} ر.س</td>
                        <td>{{ number_format($payroll->allowances, 2) }} ر.س</td>
                        <td>{{ number_format($payroll->deductions, 2) }} ر.س</td>
                        <td>
                            <span class="fw-bold text-success">{{ number_format($payroll->net_salary, 2) }} ر.س</span>
                        </td>
                        <td>
                            @switch($payroll->status)
                                @case('pending')
                                    <span class="badge bg-warning">
                                        <i class="bi bi-clock me-1"></i>معلق
                                    </span>
                                    @break
                                @case('approved')
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle me-1"></i>موافق عليه
                                    </span>
                                    @break
                                @case('rejected')
                                    <span class="badge bg-danger">
                                        <i class="bi bi-x-circle me-1"></i>مرفوض
                                    </span>
                                    @break
                                @default
                                    <span class="badge bg-secondary">{{ $payroll->status }}</span>
                            @endswitch
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('payroll.show', $payroll) }}" 
                                   class="btn btn-sm btn-outline-info" 
                                   title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </a>
                                @if($payroll->status === 'pending')
                                    <a href="{{ route('payroll.edit', $payroll) }}" 
                                       class="btn btn-sm btn-outline-primary" 
                                       title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-success" 
                                            onclick="approvePayroll({{ $payroll->id }})"
                                            title="موافقة">
                                        <i class="bi bi-check"></i>
                                    </button>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger" 
                                            onclick="rejectPayroll({{ $payroll->id }})"
                                            title="رفض">
                                        <i class="bi bi-x"></i>
                                    </button>
                                @endif
                                <button type="button" 
                                        class="btn btn-sm btn-outline-danger" 
                                        onclick="deletePayroll({{ $payroll->id }})"
                                        title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center py-5">
                            <div class="text-muted">
                                <i class="bi bi-inbox fs-1 d-block mb-3"></i>
                                <h6>لا توجد مرتبات</h6>
                                <p class="mb-0">لم يتم العثور على أي مرتبات</p>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        @if($payrolls->hasPages())
        <div class="d-flex justify-content-center mt-4">
            {{ $payrolls->links() }}
        </div>
        @endif
    </div>
</div>

<!-- Generate All Modal -->
<div class="modal fade" id="generateAllModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء مرتبات شهرية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="generateAllForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="generate_month" class="form-label">الشهر</label>
                            <select class="form-select" id="generate_month" name="month" required>
                                @for($i = 1; $i <= 12; $i++)
                                    <option value="{{ $i }}" {{ $i == date('n') ? 'selected' : '' }}>
                                        {{ \Carbon\Carbon::create()->month($i)->translatedFormat('F') }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="generate_year" class="form-label">السنة</label>
                            <select class="form-select" id="generate_year" name="year" required>
                                @for($year = date('Y'); $year >= 2020; $year--)
                                    <option value="{{ $year }}" {{ $year == date('Y') ? 'selected' : '' }}>
                                        {{ $year }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        سيتم إنشاء مرتبات لجميع الموظفين النشطين للشهر والسنة المحددين
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إنشاء المرتبات</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap-5',
        placeholder: 'اختر الموظف',
        allowClear: true
    });

    // Generate All Form
    $('#generateAllForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        Swal.fire({
            title: 'إنشاء المرتبات',
            text: 'هل أنت متأكد من إنشاء مرتبات لجميع الموظفين؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'نعم، أنشئ',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch('{{ route("payroll.generate-all") }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('تم!', data.message, 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('خطأ!', data.message, 'error');
                    }
                })
                .catch(error => {
                    Swal.fire('خطأ!', 'حدث خطأ في الشبكة', 'error');
                });

                $('#generateAllModal').modal('hide');
            }
        });
    });
});

// Approve Payroll Function
function approvePayroll(payrollId) {
    Swal.fire({
        title: 'موافقة على الراتب',
        text: 'هل أنت متأكد من الموافقة على هذا الراتب؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، وافق',
        cancelButtonText: 'إلغاء',
        input: 'textarea',
        inputPlaceholder: 'ملاحظات الموافقة (اختياري)...',
        inputAttributes: {
            'aria-label': 'ملاحظات الموافقة'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/payroll/${payrollId}/approve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    approval_notes: result.value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم!', data.message, 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ!', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.fire('خطأ!', 'حدث خطأ في الشبكة', 'error');
            });
        }
    });
}

// Reject Payroll Function
function rejectPayroll(payrollId) {
    Swal.fire({
        title: 'رفض الراتب',
        text: 'هل أنت متأكد من رفض هذا الراتب؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، ارفض',
        cancelButtonText: 'إلغاء',
        input: 'textarea',
        inputPlaceholder: 'سبب الرفض (مطلوب)...',
        inputAttributes: {
            'aria-label': 'سبب الرفض'
        },
        inputValidator: (value) => {
            if (!value) {
                return 'يرجى كتابة سبب الرفض'
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`/payroll/${payrollId}/reject`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    approval_notes: result.value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم!', data.message, 'success').then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ!', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.fire('خطأ!', 'حدث خطأ في الشبكة', 'error');
            });
        }
    });
}

// Delete Payroll Function
function deletePayroll(payrollId) {
    Swal.fire({
        title: 'حذف الراتب',
        text: 'هل أنت متأكد من حذف هذا الراتب؟ لا يمكن التراجع عن هذا الإجراء.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/payroll/${payrollId}`;

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            const methodField = document.createElement('input');
            methodField.type = 'hidden';
            methodField.name = '_method';
            methodField.value = 'DELETE';

            form.appendChild(csrfToken);
            form.appendChild(methodField);
            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>
@endpush
