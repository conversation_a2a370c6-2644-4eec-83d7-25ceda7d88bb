/**
 * Premium HRMS Effects & Animations
 * Advanced JavaScript for enhanced user experience
 */

class PremiumEffects {
    constructor() {
        this.init();
    }

    init() {
        this.setupDarkMode();
        this.setupAnimations();
        this.setupInteractions();
        this.setupNotifications();
        this.setupSearch();
    }

    // Dark Mode Toggle with Smooth Transition
    setupDarkMode() {
        const darkModeToggle = document.getElementById('darkModeToggle');
        const currentTheme = localStorage.getItem('theme') || 'light';
        
        // Set initial theme
        document.documentElement.setAttribute('data-theme', currentTheme);
        this.updateDarkModeIcon(currentTheme);

        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', () => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                // Add transition class
                document.body.style.transition = 'all 0.3s ease';
                
                // Change theme
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                
                // Update icon
                this.updateDarkModeIcon(newTheme);
                
                // Remove transition after animation
                setTimeout(() => {
                    document.body.style.transition = '';
                }, 300);
            });
        }
    }

    updateDarkModeIcon(theme) {
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            const icon = darkModeToggle.querySelector('i');
            if (icon) {
                icon.className = theme === 'dark' ? 'bi bi-sun' : 'bi bi-moon';
            }
        }
    }

    // Advanced Animations
    setupAnimations() {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe all animatable elements
        document.querySelectorAll('.animate-fade-in-up, .animate-fade-in-down, .animate-slide-in-left').forEach(el => {
            observer.observe(el);
        });

        // Parallax effect for background elements
        this.setupParallax();
    }

    setupParallax() {
        const parallaxElements = document.querySelectorAll('.parallax');
        
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            parallaxElements.forEach(element => {
                element.style.transform = `translateY(${rate}px)`;
            });
        });
    }

    // Interactive Elements
    setupInteractions() {
        // Enhanced hover effects for cards
        this.setupCardHovers();
        
        // Ripple effect for buttons
        this.setupRippleEffect();
        
        // Smooth scrolling for anchor links
        this.setupSmoothScrolling();
    }

    setupCardHovers() {
        const cards = document.querySelectorAll('.premium-card, .stats-card');
        
        cards.forEach(card => {
            card.addEventListener('mouseenter', (e) => {
                this.createHoverGlow(e.target);
            });
            
            card.addEventListener('mouseleave', (e) => {
                this.removeHoverGlow(e.target);
            });
        });
    }

    createHoverGlow(element) {
        const glow = document.createElement('div');
        glow.className = 'hover-glow';
        glow.style.cssText = `
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
            border-radius: inherit;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        `;
        
        element.style.position = 'relative';
        element.appendChild(glow);
        
        setTimeout(() => {
            glow.style.opacity = '0.3';
        }, 10);
    }

    removeHoverGlow(element) {
        const glow = element.querySelector('.hover-glow');
        if (glow) {
            glow.style.opacity = '0';
            setTimeout(() => {
                glow.remove();
            }, 300);
        }
    }

    setupRippleEffect() {
        const buttons = document.querySelectorAll('.btn-premium, .btn-primary, .btn-success');
        
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.createRipple(e);
            });
        });
    }

    createRipple(event) {
        const button = event.currentTarget;
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        `;
        
        ripple.className = 'ripple';
        button.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // Enhanced Notifications
    setupNotifications() {
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                if (alert.classList.contains('alert-dismissible')) {
                    const closeBtn = alert.querySelector('.btn-close');
                    if (closeBtn) {
                        closeBtn.click();
                    }
                }
            }, 5000);
        });

        // Toast notifications
        this.setupToastNotifications();
    }

    setupToastNotifications() {
        // Create toast container if it doesn't exist
        if (!document.querySelector('.toast-container')) {
            const container = document.createElement('div');
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
    }

    showToast(message, type = 'info', duration = 3000) {
        const container = document.querySelector('.toast-container');
        const toast = document.createElement('div');
        
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        
        toast.innerHTML = `
            <div class="toast show" role="alert" style="background: ${colors[type]}; color: white; border: none;">
                <div class="toast-body d-flex align-items-center">
                    <i class="bi bi-${this.getToastIcon(type)} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close btn-close-white ms-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        container.appendChild(toast);
        
        // Auto remove after duration
        setTimeout(() => {
            toast.remove();
        }, duration);
    }

    getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'x-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // Enhanced Search
    setupSearch() {
        const searchInput = document.querySelector('input[placeholder="البحث..."]');
        if (searchInput) {
            let searchTimeout;
            
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.performSearch(e.target.value);
                }, 300);
            });
        }
    }

    performSearch(query) {
        if (query.length < 2) return;
        
        // Add loading state
        const searchBtn = document.querySelector('input[placeholder="البحث..."]').nextElementSibling;
        const originalIcon = searchBtn.innerHTML;
        searchBtn.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
        
        // Simulate search (replace with actual search logic)
        setTimeout(() => {
            searchBtn.innerHTML = originalIcon;
            console.log('Searching for:', query);
        }, 1000);
    }

    // Utility Methods
    static formatNumber(num) {
        return new Intl.NumberFormat('ar-SA').format(num);
    }

    static formatCurrency(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(amount);
    }

    static formatDate(date) {
        return new Intl.DateTimeFormat('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    }
}

// Initialize Premium Effects when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.premiumEffects = new PremiumEffects();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PremiumEffects;
}
