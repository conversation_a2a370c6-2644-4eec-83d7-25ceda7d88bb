<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payroll extends Model
{
    protected $fillable = [
        'employee_id',
        'month',
        'year',
        'basic_salary',
        'allowances',
        'overtime_hours',
        'overtime_rate',
        'overtime_pay',
        'deductions',
        'bonus',
        'gross_salary',
        'net_salary',
        'status',
        'notes',
        'created_by',
        'approved_by',
        'approved_at',
        'approval_notes',
    ];

    protected function casts(): array
    {
        return [
            'month' => 'integer',
            'year' => 'integer',
            'basic_salary' => 'decimal:2',
            'allowances' => 'decimal:2',
            'overtime_hours' => 'decimal:2',
            'overtime_rate' => 'decimal:2',
            'overtime_pay' => 'decimal:2',
            'deductions' => 'decimal:2',
            'bonus' => 'decimal:2',
            'gross_salary' => 'decimal:2',
            'net_salary' => 'decimal:2',
            'approved_at' => 'datetime',
        ];
    }

    /**
     * Get the employee that owns the payroll.
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the user who created the payroll.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who approved the payroll.
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the month name in Arabic
     */
    public function getMonthNameAttribute(): string
    {
        $months = [
            1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
            5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
            9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر',
        ];

        return $months[$this->month] ?? '';
    }

    /**
     * Get the status in Arabic
     */
    public function getStatusNameAttribute(): string
    {
        $statuses = [
            'pending' => 'معلق',
            'approved' => 'موافق عليه',
            'rejected' => 'مرفوض',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * Calculate and update salary totals
     */
    public function calculateTotals()
    {
        $this->overtime_pay = $this->overtime_hours * $this->overtime_rate;
        $this->gross_salary = $this->basic_salary + $this->allowances + $this->overtime_pay + $this->bonus;
        $this->net_salary = $this->gross_salary - $this->deductions;

        return $this;
    }

    /**
     * Check if payroll can be edited
     */
    public function canBeEdited(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Get formatted net salary
     */
    public function getFormattedNetSalaryAttribute(): string
    {
        return number_format($this->net_salary, 2) . ' ر.س';
    }

    /**
     * Get the period string (Month Year)
     */
    public function getPeriodAttribute(): string
    {
        return $this->month_name . ' ' . $this->year;
    }
}
