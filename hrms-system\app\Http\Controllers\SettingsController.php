<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use App\Models\User;

class SettingsController extends Controller
{
    /**
     * Display the settings page
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get system settings (these would typically be stored in a settings table)
        $systemSettings = [
            'company_name' => 'شركة الموارد البشرية',
            'company_email' => '<EMAIL>',
            'company_phone' => '+966 11 123 4567',
            'company_address' => 'الرياض، المملكة العربية السعودية',
            'working_hours_start' => '08:00',
            'working_hours_end' => '17:00',
            'weekend_days' => ['friday', 'saturday'],
            'annual_leave_days' => 21,
            'sick_leave_days' => 30,
            'timezone' => 'Asia/Riyadh',
            'date_format' => 'Y/m/d',
            'currency' => 'SAR',
            'language' => 'ar'
        ];
        
        return view('settings.index', compact('user', 'systemSettings'));
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg|max:2048'
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->avatar) {
                Storage::disk('public')->delete($user->avatar);
            }
            
            $validated['avatar'] = $request->file('avatar')->store('avatars', 'public');
        }

        $user->update($validated);

        return redirect()->route('settings.index')
                        ->with('success', 'تم تحديث الملف الشخصي بنجاح');
    }

    /**
     * Update user password
     */
    public function updatePassword(Request $request)
    {
        $validated = $request->validate([
            'current_password' => 'required',
            'password' => 'required|min:6|confirmed',
        ]);

        $user = Auth::user();

        // Check if current password is correct
        if (!Hash::check($validated['current_password'], $user->password)) {
            return redirect()->back()
                           ->withErrors(['current_password' => 'كلمة المرور الحالية غير صحيحة']);
        }

        $user->update([
            'password' => Hash::make($validated['password'])
        ]);

        return redirect()->route('settings.index')
                        ->with('success', 'تم تحديث كلمة المرور بنجاح');
    }

    /**
     * Update system settings
     */
    public function updateSystem(Request $request)
    {
        $validated = $request->validate([
            'company_name' => 'required|string|max:255',
            'company_email' => 'required|email',
            'company_phone' => 'nullable|string|max:20',
            'company_address' => 'nullable|string|max:500',
            'working_hours_start' => 'required|date_format:H:i',
            'working_hours_end' => 'required|date_format:H:i|after:working_hours_start',
            'weekend_days' => 'required|array|min:1',
            'weekend_days.*' => 'in:sunday,monday,tuesday,wednesday,thursday,friday,saturday',
            'annual_leave_days' => 'required|integer|min:1|max:365',
            'sick_leave_days' => 'required|integer|min:1|max:365',
            'timezone' => 'required|string',
            'date_format' => 'required|string',
            'currency' => 'required|string|max:10',
            'language' => 'required|in:ar,en'
        ]);

        // In a real application, you would save these to a settings table
        // For now, we'll just return success
        
        return redirect()->route('settings.index')
                        ->with('success', 'تم تحديث إعدادات النظام بنجاح');
    }

    /**
     * Update notification settings
     */
    public function updateNotifications(Request $request)
    {
        $validated = $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'leave_requests' => 'boolean',
            'attendance_alerts' => 'boolean',
            'payroll_notifications' => 'boolean',
            'system_updates' => 'boolean'
        ]);

        $user = Auth::user();
        
        // In a real application, you would save these to a user_settings table
        // For now, we'll just return success
        
        return redirect()->route('settings.index')
                        ->with('success', 'تم تحديث إعدادات الإشعارات بنجاح');
    }

    /**
     * Backup system data
     */
    public function backup()
    {
        // In a real application, you would create a backup of the database
        // This is a placeholder implementation
        
        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء نسخة احتياطية من البيانات بنجاح',
            'backup_file' => 'backup_' . date('Y_m_d_H_i_s') . '.sql'
        ]);
    }

    /**
     * Clear system cache
     */
    public function clearCache()
    {
        // Clear various caches
        try {
            \Artisan::call('cache:clear');
            \Artisan::call('config:clear');
            \Artisan::call('route:clear');
            \Artisan::call('view:clear');
            
            return response()->json([
                'success' => true,
                'message' => 'تم مسح ذاكرة التخزين المؤقت بنجاح'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء مسح ذاكرة التخزين المؤقت'
            ]);
        }
    }

    /**
     * Get system information
     */
    public function systemInfo()
    {
        $info = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_version' => \DB::select('SELECT VERSION() as version')[0]->version ?? 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'disk_space' => [
                'total' => disk_total_space('.'),
                'free' => disk_free_space('.')
            ]
        ];
        
        return response()->json($info);
    }

    /**
     * Test email configuration
     */
    public function testEmail(Request $request)
    {
        $validated = $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            // In a real application, you would send a test email
            // For now, we'll just simulate success
            
            return response()->json([
                'success' => true,
                'message' => 'تم إرسال بريد إلكتروني تجريبي إلى ' . $validated['test_email']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في إرسال البريد الإلكتروني: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Export system settings
     */
    public function exportSettings()
    {
        // Get all system settings
        $settings = [
            'company_name' => 'شركة الموارد البشرية',
            'company_email' => '<EMAIL>',
            'working_hours_start' => '08:00',
            'working_hours_end' => '17:00',
            'annual_leave_days' => 21,
            'sick_leave_days' => 30,
            // Add more settings as needed
        ];

        $filename = 'hrms_settings_' . date('Y_m_d_H_i_s') . '.json';
        
        return response()->json($settings)
                        ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Import system settings
     */
    public function importSettings(Request $request)
    {
        $validated = $request->validate([
            'settings_file' => 'required|file|mimes:json'
        ]);

        try {
            $content = file_get_contents($validated['settings_file']->path());
            $settings = json_decode($content, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('ملف الإعدادات غير صالح');
            }

            // In a real application, you would validate and save these settings
            
            return redirect()->route('settings.index')
                           ->with('success', 'تم استيراد الإعدادات بنجاح');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->withErrors(['settings_file' => 'خطأ في استيراد الإعدادات: ' . $e->getMessage()]);
        }
    }
}
