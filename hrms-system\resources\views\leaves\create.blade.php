@extends('layouts.app')

@section('title', 'طلب إجازة جديد')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4 animate-fade-in-down">
    <div>
        <h1 class="h3 mb-2 gradient-text fw-bold">
            <i class="bi bi-plus-circle me-2"></i>
            طلب إجازة جديد
        </h1>
        <p class="text-muted mb-0">تقديم طلب إجازة للموظف</p>
    </div>
    <a href="{{ route('leaves.index') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-right me-2"></i>
        العودة للقائمة
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="premium-card animate-fade-in-up">
            <div class="card-header bg-transparent border-0 py-4">
                <h5 class="mb-0 fw-bold">
                    <i class="bi bi-calendar-plus me-2 text-primary"></i>
                    بيانات طلب الإجازة
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('leaves.store') }}" method="POST" id="leaveForm" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="row">
                        <!-- اختيار الموظف -->
                        <div class="col-md-6 mb-4">
                            <label for="employee_id" class="form-label fw-semibold">
                                <i class="bi bi-person me-1"></i>
                                الموظف <span class="text-danger">*</span>
                            </label>
                            <select class="form-select select2 @error('employee_id') is-invalid @enderror" 
                                    id="employee_id" 
                                    name="employee_id" 
                                    required>
                                <option value="">اختر الموظف</option>
                                @foreach($employees as $employee)
                                    <option value="{{ $employee->id }}" {{ old('employee_id') == $employee->id ? 'selected' : '' }}>
                                        {{ $employee->full_name }} ({{ $employee->employee_id }})
                                    </option>
                                @endforeach
                            </select>
                            @error('employee_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- نوع الإجازة -->
                        <div class="col-md-6 mb-4">
                            <label for="type" class="form-label fw-semibold">
                                <i class="bi bi-tag me-1"></i>
                                نوع الإجازة <span class="text-danger">*</span>
                            </label>
                            <select class="form-select @error('type') is-invalid @enderror" 
                                    id="type" 
                                    name="type" 
                                    required>
                                <option value="">اختر نوع الإجازة</option>
                                <option value="annual" {{ old('type') == 'annual' ? 'selected' : '' }}>
                                    <i class="bi bi-calendar-heart"></i> إجازة سنوية
                                </option>
                                <option value="sick" {{ old('type') == 'sick' ? 'selected' : '' }}>
                                    <i class="bi bi-heart-pulse"></i> إجازة مرضية
                                </option>
                                <option value="emergency" {{ old('type') == 'emergency' ? 'selected' : '' }}>
                                    <i class="bi bi-exclamation-triangle"></i> إجازة طارئة
                                </option>
                                <option value="maternity" {{ old('type') == 'maternity' ? 'selected' : '' }}>
                                    <i class="bi bi-person-hearts"></i> إجازة أمومة
                                </option>
                                <option value="paternity" {{ old('type') == 'paternity' ? 'selected' : '' }}>
                                    <i class="bi bi-person-check"></i> إجازة أبوة
                                </option>
                                <option value="unpaid" {{ old('type') == 'unpaid' ? 'selected' : '' }}>
                                    <i class="bi bi-cash-stack"></i> إجازة بدون راتب
                                </option>
                            </select>
                            @error('type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <!-- تاريخ البداية -->
                        <div class="col-md-6 mb-4">
                            <label for="start_date" class="form-label fw-semibold">
                                <i class="bi bi-calendar-event me-1 text-success"></i>
                                تاريخ البداية <span class="text-danger">*</span>
                            </label>
                            <input type="date" 
                                   class="form-control @error('start_date') is-invalid @enderror" 
                                   id="start_date" 
                                   name="start_date" 
                                   value="{{ old('start_date') }}" 
                                   min="{{ date('Y-m-d') }}"
                                   required>
                            @error('start_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- تاريخ النهاية -->
                        <div class="col-md-6 mb-4">
                            <label for="end_date" class="form-label fw-semibold">
                                <i class="bi bi-calendar-x me-1 text-danger"></i>
                                تاريخ النهاية <span class="text-danger">*</span>
                            </label>
                            <input type="date" 
                                   class="form-control @error('end_date') is-invalid @enderror" 
                                   id="end_date" 
                                   name="end_date" 
                                   value="{{ old('end_date') }}" 
                                   min="{{ date('Y-m-d') }}"
                                   required>
                            @error('end_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <!-- عدد الأيام المطلوبة -->
                        <div class="col-md-6 mb-4">
                            <label for="days_requested" class="form-label fw-semibold">
                                <i class="bi bi-calendar-range me-1"></i>
                                عدد الأيام المطلوبة
                            </label>
                            <input type="number" 
                                   class="form-control @error('days_requested') is-invalid @enderror" 
                                   id="days_requested" 
                                   name="days_requested" 
                                   min="1" 
                                   max="365"
                                   value="{{ old('days_requested') }}" 
                                   readonly>
                            @error('days_requested')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">سيتم حساب عدد الأيام تلقائياً</div>
                        </div>

                        <!-- إجازة نصف يوم -->
                        <div class="col-md-6 mb-4">
                            <label class="form-label fw-semibold">
                                <i class="bi bi-clock-split me-1"></i>
                                نوع الإجازة
                            </label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_half_day" name="is_half_day" value="1" {{ old('is_half_day') ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_half_day">
                                    إجازة نصف يوم
                                </label>
                            </div>
                            <div id="half_day_period_container" style="display: none;" class="mt-2">
                                <select class="form-select" id="half_day_period" name="half_day_period">
                                    <option value="">اختر الفترة</option>
                                    <option value="morning" {{ old('half_day_period') == 'morning' ? 'selected' : '' }}>الفترة الصباحية</option>
                                    <option value="afternoon" {{ old('half_day_period') == 'afternoon' ? 'selected' : '' }}>الفترة المسائية</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- سبب الإجازة -->
                    <div class="mb-4">
                        <label for="reason" class="form-label fw-semibold">
                            <i class="bi bi-chat-text me-1"></i>
                            سبب الإجازة <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('reason') is-invalid @enderror" 
                                  id="reason" 
                                  name="reason" 
                                  rows="4" 
                                  placeholder="اكتب سبب طلب الإجازة بالتفصيل..."
                                  required>{{ old('reason') }}</textarea>
                        @error('reason')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- رفع مرفق -->
                    <div class="mb-4">
                        <label for="attachment" class="form-label fw-semibold">
                            <i class="bi bi-paperclip me-1"></i>
                            مرفق (اختياري)
                        </label>
                        <input type="file" 
                               class="form-control @error('attachment') is-invalid @enderror" 
                               id="attachment" 
                               name="attachment"
                               accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                        @error('attachment')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">يمكنك رفع ملف PDF أو Word أو صورة (حد أقصى 5MB)</div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="bi bi-info-circle me-2"></i>
                            معلومات مهمة:
                        </h6>
                        <ul class="mb-0">
                            <li>سيتم مراجعة طلبك من قبل المدير المباشر</li>
                            <li>ستصلك إشعارات بحالة الطلب عبر البريد الإلكتروني</li>
                            <li>يمكنك تعديل الطلب قبل الموافقة عليه</li>
                            <li>تأكد من صحة جميع البيانات قبل الإرسال</li>
                        </ul>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                            <i class="bi bi-x-circle me-2"></i>
                            إلغاء
                        </button>
                        <div>
                            <button type="reset" class="btn btn-outline-warning me-2">
                                <i class="bi bi-arrow-clockwise me-2"></i>
                                إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-premium">
                                <i class="bi bi-send me-2"></i>
                                إرسال الطلب
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Employee Leave Balance Modal -->
<div class="modal fade" id="leaveBalanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رصيد الإجازات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="leaveBalanceContent">
                <!-- سيتم تحميل المحتوى عبر AJAX -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap-5',
        placeholder: 'اختر الموظف',
        allowClear: true
    });

    // Calculate days automatically
    function calculateDays() {
        const startDate = $('#start_date').val();
        const endDate = $('#end_date').val();
        const isHalfDay = $('#is_half_day').is(':checked');
        
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            
            if (end >= start) {
                const timeDiff = end.getTime() - start.getTime();
                let daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
                
                if (isHalfDay && daysDiff === 1) {
                    daysDiff = 0.5;
                }
                
                $('#days_requested').val(daysDiff);
            }
        }
    }

    // Auto-calculate days when dates change
    $('#start_date, #end_date').on('change', calculateDays);

    // Handle half day checkbox
    $('#is_half_day').on('change', function() {
        if ($(this).is(':checked')) {
            $('#half_day_period_container').show();
            $('#half_day_period').prop('required', true);
        } else {
            $('#half_day_period_container').hide();
            $('#half_day_period').prop('required', false);
        }
        calculateDays();
    });

    // Set minimum end date based on start date
    $('#start_date').on('change', function() {
        const startDate = $(this).val();
        $('#end_date').attr('min', startDate);
        if ($('#end_date').val() < startDate) {
            $('#end_date').val(startDate);
        }
        calculateDays();
    });

    // Show employee leave balance when employee is selected
    $('#employee_id').on('change', function() {
        const employeeId = $(this).val();
        if (employeeId) {
            loadEmployeeLeaveBalance(employeeId);
        }
    });

    // Load employee leave balance
    function loadEmployeeLeaveBalance(employeeId) {
        $.ajax({
            url: `/employees/${employeeId}/leave-balance`,
            method: 'GET',
            success: function(data) {
                let balanceHtml = '<div class="row">';
                
                if (data.annual_balance !== undefined) {
                    balanceHtml += `
                        <div class="col-6 mb-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h6 class="text-primary">الإجازة السنوية</h6>
                                <h4 class="mb-0">${data.annual_balance} يوم</h4>
                            </div>
                        </div>
                    `;
                }
                
                if (data.sick_balance !== undefined) {
                    balanceHtml += `
                        <div class="col-6 mb-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h6 class="text-success">الإجازة المرضية</h6>
                                <h4 class="mb-0">${data.sick_balance} يوم</h4>
                            </div>
                        </div>
                    `;
                }
                
                balanceHtml += '</div>';
                
                $('#leaveBalanceContent').html(balanceHtml);
                $('#leaveBalanceModal').modal('show');
            },
            error: function() {
                $('#leaveBalanceContent').html('<div class="alert alert-warning">لا يمكن تحميل رصيد الإجازات</div>');
                $('#leaveBalanceModal').modal('show');
            }
        });
    }

    // Form validation
    $('#leaveForm').on('submit', function(e) {
        const employeeId = $('#employee_id').val();
        const type = $('#type').val();
        const startDate = $('#start_date').val();
        const endDate = $('#end_date').val();
        const reason = $('#reason').val().trim();
        
        if (!employeeId || !type || !startDate || !endDate || !reason) {
            e.preventDefault();
            Swal.fire({
                title: 'خطأ في البيانات',
                text: 'يرجى ملء جميع الحقول المطلوبة',
                icon: 'error',
                confirmButtonText: 'موافق'
            });
            return false;
        }
        
        // Show loading
        Swal.fire({
            title: 'جاري إرسال الطلب...',
            text: 'يرجى الانتظار',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    });

    // Initialize half day display
    if ($('#is_half_day').is(':checked')) {
        $('#half_day_period_container').show();
        $('#half_day_period').prop('required', true);
    }
});
</script>
@endpush

@push('styles')
<style>
.premium-card {
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
    border-radius: 15px;
}

.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-premium {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 25px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-premium:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.select2-container--bootstrap-5 .select2-selection {
    border-color: #dee2e6;
}

.select2-container--bootstrap-5 .select2-selection:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}
</style>
@endpush
