@extends('layouts.app')

@section('title', 'إضافة موظف جديد')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="bi bi-person-plus me-2"></i>
        إضافة موظف جديد
    </h1>
    <a href="{{ route('employees.index') }}" class="btn btn-secondary">
        <i class="bi bi-arrow-left me-2"></i>
        العودة للقائمة
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="bi bi-person-fill-add me-2"></i>
            بيانات الموظف الجديد
        </h6>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ route('employees.store') }}" enctype="multipart/form-data">
            @csrf
            
            <!-- Personal Information -->
            <div class="row mb-4">
                <div class="col-12">
                    <h5 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-person me-2"></i>
                        المعلومات الشخصية
                    </h5>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="first_name" class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('first_name') is-invalid @enderror" 
                           id="first_name" name="first_name" value="{{ old('first_name') }}" required>
                    @error('first_name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="last_name" class="form-label">الاسم الأخير <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('last_name') is-invalid @enderror" 
                           id="last_name" name="last_name" value="{{ old('last_name') }}" required>
                    @error('last_name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="first_name_ar" class="form-label">الاسم الأول (عربي)</label>
                    <input type="text" class="form-control @error('first_name_ar') is-invalid @enderror" 
                           id="first_name_ar" name="first_name_ar" value="{{ old('first_name_ar') }}">
                    @error('first_name_ar')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="last_name_ar" class="form-label">الاسم الأخير (عربي)</label>
                    <input type="text" class="form-control @error('last_name_ar') is-invalid @enderror" 
                           id="last_name_ar" name="last_name_ar" value="{{ old('last_name_ar') }}">
                    @error('last_name_ar')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                           id="email" name="email" value="{{ old('email') }}" required>
                    @error('email')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label">رقم الهاتف</label>
                    <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                           id="phone" name="phone" value="{{ old('phone') }}">
                    @error('phone')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="national_id" class="form-label">رقم الهوية <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('national_id') is-invalid @enderror" 
                           id="national_id" name="national_id" value="{{ old('national_id') }}" required>
                    @error('national_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                    <input type="date" class="form-control datepicker @error('birth_date') is-invalid @enderror" 
                           id="birth_date" name="birth_date" value="{{ old('birth_date') }}">
                    @error('birth_date')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="gender" class="form-label">الجنس</label>
                    <select class="form-select @error('gender') is-invalid @enderror" id="gender" name="gender">
                        <option value="">اختر الجنس</option>
                        <option value="male" {{ old('gender') == 'male' ? 'selected' : '' }}>ذكر</option>
                        <option value="female" {{ old('gender') == 'female' ? 'selected' : '' }}>أنثى</option>
                    </select>
                    @error('gender')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="marital_status" class="form-label">الحالة الاجتماعية</label>
                    <select class="form-select @error('marital_status') is-invalid @enderror" id="marital_status" name="marital_status">
                        <option value="">اختر الحالة الاجتماعية</option>
                        <option value="single" {{ old('marital_status') == 'single' ? 'selected' : '' }}>أعزب</option>
                        <option value="married" {{ old('marital_status') == 'married' ? 'selected' : '' }}>متزوج</option>
                        <option value="divorced" {{ old('marital_status') == 'divorced' ? 'selected' : '' }}>مطلق</option>
                        <option value="widowed" {{ old('marital_status') == 'widowed' ? 'selected' : '' }}>أرمل</option>
                    </select>
                    @error('marital_status')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-12 mb-3">
                    <label for="address" class="form-label">العنوان</label>
                    <textarea class="form-control @error('address') is-invalid @enderror" 
                              id="address" name="address" rows="3">{{ old('address') }}</textarea>
                    @error('address')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
            
            <!-- Job Information -->
            <div class="row mb-4">
                <div class="col-12">
                    <h5 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-briefcase me-2"></i>
                        معلومات الوظيفة
                    </h5>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="department_id" class="form-label">القسم <span class="text-danger">*</span></label>
                    <select class="form-select select2 @error('department_id') is-invalid @enderror" 
                            id="department_id" name="department_id" required>
                        <option value="">اختر القسم</option>
                        @foreach($departments as $department)
                            <option value="{{ $department->id }}" {{ old('department_id') == $department->id ? 'selected' : '' }}>
                                {{ $department->name_ar ?? $department->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('department_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="position" class="form-label">المنصب <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('position') is-invalid @enderror" 
                           id="position" name="position" value="{{ old('position') }}" required>
                    @error('position')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="position_ar" class="form-label">المنصب (عربي)</label>
                    <input type="text" class="form-control @error('position_ar') is-invalid @enderror" 
                           id="position_ar" name="position_ar" value="{{ old('position_ar') }}">
                    @error('position_ar')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="hire_date" class="form-label">تاريخ التوظيف <span class="text-danger">*</span></label>
                    <input type="date" class="form-control datepicker @error('hire_date') is-invalid @enderror" 
                           id="hire_date" name="hire_date" value="{{ old('hire_date', date('Y-m-d')) }}" required>
                    @error('hire_date')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="salary" class="form-label">الراتب الأساسي <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="number" class="form-control @error('salary') is-invalid @enderror" 
                               id="salary" name="salary" value="{{ old('salary') }}" step="0.01" min="0" required>
                        <span class="input-group-text">ر.س</span>
                    </div>
                    @error('salary')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="contract_type" class="form-label">نوع العقد</label>
                    <select class="form-select @error('contract_type') is-invalid @enderror" id="contract_type" name="contract_type">
                        <option value="full_time" {{ old('contract_type') == 'full_time' ? 'selected' : '' }}>دوام كامل</option>
                        <option value="part_time" {{ old('contract_type') == 'part_time' ? 'selected' : '' }}>دوام جزئي</option>
                        <option value="contract" {{ old('contract_type') == 'contract' ? 'selected' : '' }}>عقد مؤقت</option>
                        <option value="internship" {{ old('contract_type') == 'internship' ? 'selected' : '' }}>تدريب</option>
                    </select>
                    @error('contract_type')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
            
            <!-- Emergency Contact -->
            <div class="row mb-4">
                <div class="col-12">
                    <h5 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-telephone me-2"></i>
                        جهة الاتصال في حالات الطوارئ
                    </h5>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="emergency_contact_name" class="form-label">اسم جهة الاتصال</label>
                    <input type="text" class="form-control @error('emergency_contact_name') is-invalid @enderror" 
                           id="emergency_contact_name" name="emergency_contact_name" value="{{ old('emergency_contact_name') }}">
                    @error('emergency_contact_name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="emergency_contact_phone" class="form-label">رقم هاتف جهة الاتصال</label>
                    <input type="tel" class="form-control @error('emergency_contact_phone') is-invalid @enderror" 
                           id="emergency_contact_phone" name="emergency_contact_phone" value="{{ old('emergency_contact_phone') }}">
                    @error('emergency_contact_phone')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
            
            <!-- Bank Information -->
            <div class="row mb-4">
                <div class="col-12">
                    <h5 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-bank me-2"></i>
                        المعلومات البنكية
                    </h5>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="bank_name" class="form-label">اسم البنك</label>
                    <input type="text" class="form-control @error('bank_name') is-invalid @enderror" 
                           id="bank_name" name="bank_name" value="{{ old('bank_name') }}">
                    @error('bank_name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="bank_account" class="form-label">رقم الحساب البنكي</label>
                    <input type="text" class="form-control @error('bank_account') is-invalid @enderror" 
                           id="bank_account" name="bank_account" value="{{ old('bank_account') }}">
                    @error('bank_account')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
            
            <!-- Profile Picture -->
            <div class="row mb-4">
                <div class="col-12">
                    <h5 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-image me-2"></i>
                        الصورة الشخصية
                    </h5>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="profile_picture" class="form-label">رفع الصورة الشخصية</label>
                    <input type="file" class="form-control @error('profile_picture') is-invalid @enderror" 
                           id="profile_picture" name="profile_picture" accept="image/*">
                    <div class="form-text">الحد الأقصى: 2MB، الصيغ المدعومة: JPG, PNG</div>
                    @error('profile_picture')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="col-md-6 mb-3">
                    <div id="imagePreview" class="text-center" style="display: none;">
                        <img id="preview" src="" alt="معاينة الصورة" class="img-thumbnail" style="max-width: 200px;">
                    </div>
                </div>
            </div>
            
            <!-- User Account -->
            <div class="row mb-4">
                <div class="col-12">
                    <h5 class="text-primary border-bottom pb-2 mb-3">
                        <i class="bi bi-person-gear me-2"></i>
                        حساب المستخدم
                    </h5>
                </div>
                
                <div class="col-12 mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="create_user_account" name="create_user_account" value="1">
                        <label class="form-check-label" for="create_user_account">
                            إنشاء حساب مستخدم للموظف
                        </label>
                    </div>
                </div>
                
                <div id="userAccountFields" style="display: none;">
                    <div class="col-md-6 mb-3">
                        <label for="role_id" class="form-label">الدور</label>
                        <select class="form-select select2 @error('role_id') is-invalid @enderror" id="role_id" name="role_id">
                            <option value="">اختر الدور</option>
                            @foreach($roles as $role)
                                <option value="{{ $role->id }}" {{ old('role_id') == $role->id ? 'selected' : '' }}>
                                    {{ $role->name_ar ?? $role->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('role_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control @error('password') is-invalid @enderror" 
                               id="password" name="password" placeholder="اتركه فارغاً لاستخدام كلمة المرور الافتراضية (123456)">
                        @error('password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Submit Buttons -->
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ route('employees.index') }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            حفظ الموظف
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Toggle user account fields
    $('#create_user_account').change(function() {
        if ($(this).is(':checked')) {
            $('#userAccountFields').show();
        } else {
            $('#userAccountFields').hide();
        }
    });
    
    // Image preview
    $('#profile_picture').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#preview').attr('src', e.target.result);
                $('#imagePreview').show();
            };
            reader.readAsDataURL(file);
        } else {
            $('#imagePreview').hide();
        }
    });
    
    // Form validation
    $('form').on('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        $('input[required], select[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'خطأ في البيانات',
                text: 'يرجى ملء جميع الحقول المطلوبة',
                confirmButtonText: 'موافق'
            });
        }
    });
});
</script>
@endpush
