@extends('layouts.app')

@section('title', 'تفاصيل الموظف - ' . $employee->full_name)

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="bi bi-person me-2"></i>
        تفاصيل الموظف
    </h1>
    <div class="btn-group">
        <a href="{{ route('employees.index') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            العودة للقائمة
        </a>
        <a href="{{ route('employees.edit', $employee) }}" class="btn btn-primary">
            <i class="bi bi-pencil me-2"></i>
            تعديل البيانات
        </a>
    </div>
</div>

<!-- Employee Profile Card -->
<div class="row">
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-body text-center">
                @if($employee->profile_picture)
                    <img src="{{ asset('storage/' . $employee->profile_picture) }}" 
                         alt="{{ $employee->full_name }}" 
                         class="rounded-circle mb-3" 
                         width="150" height="150">
                @else
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 150px; height: 150px;">
                        <i class="bi bi-person text-white" style="font-size: 4rem;"></i>
                    </div>
                @endif
                
                <h4 class="mb-1">{{ $employee->full_name }}</h4>
                @if($employee->full_name_ar)
                    <p class="text-muted mb-2">{{ $employee->full_name_ar }}</p>
                @endif
                
                <span class="badge bg-primary fs-6 mb-3">{{ $employee->employee_id }}</span>
                
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="mb-0">{{ $employee->attendances->count() }}</h5>
                            <small class="text-muted">أيام الحضور</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="mb-0">{{ $employee->leaves->count() }}</h5>
                            <small class="text-muted">الإجازات</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h5 class="mb-0">{{ $employee->annual_leave_balance }}</h5>
                        <small class="text-muted">رصيد الإجازات</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('attendance.create', ['employee' => $employee->id]) }}" class="btn btn-success btn-sm">
                        <i class="bi bi-clock me-2"></i>
                        تسجيل حضور
                    </a>
                    <a href="{{ route('leaves.create', ['employee' => $employee->id]) }}" class="btn btn-warning btn-sm">
                        <i class="bi bi-calendar-plus me-2"></i>
                        طلب إجازة
                    </a>
                    <a href="{{ route('payroll.create', ['employee' => $employee->id]) }}" class="btn btn-info btn-sm">
                        <i class="bi bi-cash-stack me-2"></i>
                        إنشاء مرتب
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-8">
        <!-- Personal Information -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-person-fill me-2"></i>
                    المعلومات الشخصية
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>البريد الإلكتروني:</strong>
                        <p class="mb-0">{{ $employee->email }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>رقم الهاتف:</strong>
                        <p class="mb-0">{{ $employee->phone ?? 'غير محدد' }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>رقم الهوية:</strong>
                        <p class="mb-0">{{ $employee->national_id }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>تاريخ الميلاد:</strong>
                        <p class="mb-0">{{ $employee->birth_date ? $employee->birth_date->format('Y/m/d') : 'غير محدد' }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>الجنس:</strong>
                        <p class="mb-0">
                            @if($employee->gender == 'male')
                                ذكر
                            @elseif($employee->gender == 'female')
                                أنثى
                            @else
                                غير محدد
                            @endif
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>الحالة الاجتماعية:</strong>
                        <p class="mb-0">
                            @switch($employee->marital_status)
                                @case('single')
                                    أعزب
                                    @break
                                @case('married')
                                    متزوج
                                    @break
                                @case('divorced')
                                    مطلق
                                    @break
                                @case('widowed')
                                    أرمل
                                    @break
                                @default
                                    غير محدد
                            @endswitch
                        </p>
                    </div>
                    @if($employee->address)
                    <div class="col-12 mb-3">
                        <strong>العنوان:</strong>
                        <p class="mb-0">{{ $employee->address }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Job Information -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-briefcase-fill me-2"></i>
                    معلومات الوظيفة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>القسم:</strong>
                        <p class="mb-0">
                            @if($employee->department)
                                <span class="badge bg-info">
                                    {{ $employee->department->name_ar ?? $employee->department->name }}
                                </span>
                            @else
                                غير محدد
                            @endif
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>المنصب:</strong>
                        <p class="mb-0">{{ $employee->position_ar ?? $employee->position }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>تاريخ التوظيف:</strong>
                        <p class="mb-0">{{ $employee->hire_date->format('Y/m/d') }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>الراتب الأساسي:</strong>
                        <p class="mb-0">{{ number_format($employee->salary, 2) }} ر.س</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>نوع العقد:</strong>
                        <p class="mb-0">
                            @switch($employee->contract_type)
                                @case('full_time')
                                    دوام كامل
                                    @break
                                @case('part_time')
                                    دوام جزئي
                                    @break
                                @case('contract')
                                    عقد مؤقت
                                    @break
                                @case('internship')
                                    تدريب
                                    @break
                                @default
                                    غير محدد
                            @endswitch
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>حالة التوظيف:</strong>
                        <p class="mb-0">
                            @switch($employee->employment_status)
                                @case('active')
                                    <span class="badge bg-success">نشط</span>
                                    @break
                                @case('inactive')
                                    <span class="badge bg-warning">غير نشط</span>
                                    @break
                                @case('terminated')
                                    <span class="badge bg-danger">منتهي الخدمة</span>
                                    @break
                                @default
                                    <span class="badge bg-secondary">غير محدد</span>
                            @endswitch
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Emergency Contact -->
        @if($employee->emergency_contact_name || $employee->emergency_contact_phone)
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-telephone-fill me-2"></i>
                    جهة الاتصال في حالات الطوارئ
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    @if($employee->emergency_contact_name)
                    <div class="col-md-6 mb-3">
                        <strong>الاسم:</strong>
                        <p class="mb-0">{{ $employee->emergency_contact_name }}</p>
                    </div>
                    @endif
                    @if($employee->emergency_contact_phone)
                    <div class="col-md-6 mb-3">
                        <strong>رقم الهاتف:</strong>
                        <p class="mb-0">{{ $employee->emergency_contact_phone }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        @endif
        
        <!-- Bank Information -->
        @if($employee->bank_name || $employee->bank_account)
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-bank me-2"></i>
                    المعلومات البنكية
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    @if($employee->bank_name)
                    <div class="col-md-6 mb-3">
                        <strong>اسم البنك:</strong>
                        <p class="mb-0">{{ $employee->bank_name }}</p>
                    </div>
                    @endif
                    @if($employee->bank_account)
                    <div class="col-md-6 mb-3">
                        <strong>رقم الحساب:</strong>
                        <p class="mb-0">{{ $employee->bank_account }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clock-history me-2"></i>
                    آخر سجلات الحضور
                </h6>
            </div>
            <div class="card-body">
                @if($employee->attendances->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>دخول</th>
                                    <th>خروج</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($employee->attendances->take(5) as $attendance)
                                <tr>
                                    <td>{{ $attendance->date->format('Y/m/d') }}</td>
                                    <td>{{ $attendance->check_in ? $attendance->check_in->format('H:i') : '-' }}</td>
                                    <td>{{ $attendance->check_out ? $attendance->check_out->format('H:i') : '-' }}</td>
                                    <td>
                                        @switch($attendance->status)
                                            @case('present')
                                                <span class="badge bg-success">حاضر</span>
                                                @break
                                            @case('absent')
                                                <span class="badge bg-danger">غائب</span>
                                                @break
                                            @case('late')
                                                <span class="badge bg-warning">متأخر</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">{{ $attendance->status }}</span>
                                        @endswitch
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted text-center">لا توجد سجلات حضور</p>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-calendar-event me-2"></i>
                    آخر طلبات الإجازات
                </h6>
            </div>
            <div class="card-body">
                @if($employee->leaves->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>النوع</th>
                                    <th>من</th>
                                    <th>إلى</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($employee->leaves->take(5) as $leave)
                                <tr>
                                    <td>
                                        @switch($leave->type)
                                            @case('annual')
                                                سنوية
                                                @break
                                            @case('sick')
                                                مرضية
                                                @break
                                            @case('emergency')
                                                طارئة
                                                @break
                                            @default
                                                {{ $leave->type }}
                                        @endswitch
                                    </td>
                                    <td>{{ $leave->start_date->format('m/d') }}</td>
                                    <td>{{ $leave->end_date->format('m/d') }}</td>
                                    <td>
                                        @switch($leave->status)
                                            @case('pending')
                                                <span class="badge bg-warning">معلقة</span>
                                                @break
                                            @case('approved')
                                                <span class="badge bg-success">موافق عليها</span>
                                                @break
                                            @case('rejected')
                                                <span class="badge bg-danger">مرفوضة</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">{{ $leave->status }}</span>
                                        @endswitch
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted text-center">لا توجد طلبات إجازات</p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
