@extends('layouts.app')

@section('title', 'لوحة التحكم')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-5 animate-fade-in-down">
    <div>
        <h1 class="h2 mb-2 gradient-text fw-bold">
            <i class="bi bi-speedometer2 me-3"></i>
            لوحة التحكم الرئيسية
        </h1>
        <p class="text-muted mb-0">نظرة شاملة على أداء النظام والإحصائيات المهمة</p>
    </div>
    <div class="d-flex align-items-center">
        <div class="text-end me-3">
            <div class="fw-semibold text-dark">{{ now()->format('l') }}</div>
            <small class="text-muted">{{ now()->format('F j, Y') }}</small>
        </div>
        <div class="bg-primary rounded-circle p-3 animate-pulse">
            <i class="bi bi-calendar3 text-white fs-4"></i>
        </div>
    </div>
</div>

<!-- Premium Statistics Cards -->
<div class="row mb-5">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.1s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-primary mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        إجمالي الموظفين
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark" id="totalEmployees">
                        {{ $totalEmployees }}
                    </div>
                    <div class="text-success small mt-1">
                        <i class="bi bi-arrow-up"></i> +5% من الشهر الماضي
                    </div>
                </div>
                <div class="bg-primary bg-gradient rounded-circle p-3 animate-float">
                    <i class="bi bi-people-fill text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.2s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-success mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        الحضور اليوم
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark" id="todayAttendance">
                        {{ $todayAttendance }}
                    </div>
                    <div class="text-info small mt-1">
                        <i class="bi bi-clock"></i> معدل الحضور 95%
                    </div>
                </div>
                <div class="bg-success bg-gradient rounded-circle p-3 animate-float" style="animation-delay: 0.5s;">
                    <i class="bi bi-clock-fill text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.3s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-warning mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        الإجازات المعلقة
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark" id="pendingLeaves">
                        {{ $pendingLeaves }}
                    </div>
                    <div class="text-warning small mt-1">
                        <i class="bi bi-exclamation-triangle"></i> تحتاج مراجعة
                    </div>
                </div>
                <div class="bg-warning bg-gradient rounded-circle p-3 animate-float" style="animation-delay: 1s;">
                    <i class="bi bi-calendar-check-fill text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card animate-fade-in-up" style="animation-delay: 0.4s;">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <div class="text-uppercase fw-bold text-info mb-2" style="font-size: 12px; letter-spacing: 1px;">
                        مرتبات هذا الشهر
                    </div>
                    <div class="h2 mb-0 fw-bold text-dark" id="monthlyPayroll">
                        {{ number_format($monthlyPayroll, 0) }}
                    </div>
                    <div class="text-muted small mt-1">ر.س</div>
                </div>
                <div class="bg-info bg-gradient rounded-circle p-3 animate-float" style="animation-delay: 1.5s;">
                    <i class="bi bi-cash-stack text-white fs-4"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Premium Charts Row -->
<div class="row mb-5">
    <!-- Attendance Chart -->
    <div class="col-xl-8 col-lg-7 mb-4">
        <div class="premium-card animate-fade-in-up" style="animation-delay: 0.5s;">
            <div class="card-header bg-transparent border-0 py-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1 fw-bold gradient-text">
                            <i class="bi bi-graph-up-arrow me-2"></i>
                            إحصائيات الحضور الشهرية
                        </h5>
                        <p class="text-muted mb-0 small">تتبع معدلات الحضور والغياب على مدار الشهر</p>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-three-dots"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-download me-2"></i>تصدير البيانات</a></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-printer me-2"></i>طباعة</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body pt-0">
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="attendanceChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Stats -->
    <div class="col-xl-4 col-lg-5 mb-4">
        <div class="premium-card animate-fade-in-up" style="animation-delay: 0.6s;">
            <div class="card-header bg-transparent border-0 py-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1 fw-bold gradient-text">
                            <i class="bi bi-diagram-3-fill me-2"></i>
                            توزيع الموظفين
                        </h5>
                        <p class="text-muted mb-0 small">حسب الأقسام المختلفة</p>
                    </div>
                </div>
            </div>
            <div class="card-body pt-0">
                <div class="chart-container" style="position: relative; height: 300px;">
                    <canvas id="departmentChart"></canvas>
                </div>
                <!-- Department Legend -->
                <div class="mt-3" id="departmentLegend">
                    @foreach($departmentStats as $index => $dept)
                    <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle me-2" style="width: 12px; height: 12px; background-color: {{ ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'][$index % 6] }};"></div>
                            <span class="small">{{ $dept->name_ar ?? $dept->name }}</span>
                        </div>
                        <span class="badge bg-light text-dark">{{ $dept->employees_count }}</span>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Premium Activities & Actions Row -->
<div class="row">
    <!-- Recent Activities -->
    <div class="col-lg-8 mb-4">
        <div class="premium-card animate-fade-in-up" style="animation-delay: 0.7s;">
            <div class="card-header bg-transparent border-0 py-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1 fw-bold gradient-text">
                            <i class="bi bi-activity me-2"></i>
                            الأنشطة الأخيرة
                        </h5>
                        <p class="text-muted mb-0 small">آخر العمليات والتحديثات في النظام</p>
                    </div>
                    <a href="#" class="btn btn-sm btn-outline-primary">
                        عرض الكل <i class="bi bi-arrow-left ms-1"></i>
                    </a>
                </div>
            </div>
            <div class="card-body pt-0">
                <div class="activity-timeline">
                    @forelse($recentActivities as $index => $activity)
                    <div class="activity-item d-flex align-items-start mb-4 animate-slide-in-left" style="animation-delay: {{ 0.8 + ($index * 0.1) }}s;">
                        <div class="activity-icon me-3">
                            <div class="bg-{{ $activity->status === 'present' ? 'success' : 'danger' }} bg-gradient rounded-circle d-flex align-items-center justify-content-center" style="width: 45px; height: 45px;">
                                <i class="bi bi-{{ $activity->status === 'present' ? 'check-circle' : 'x-circle' }} text-white"></i>
                            </div>
                        </div>
                        <div class="activity-content flex-grow-1">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1 fw-semibold">{{ $activity->employee->full_name }}</h6>
                                    <p class="mb-1 text-muted small">
                                        {{ $activity->status === 'present' ? 'سجل حضوره' : 'تم تسجيل غيابه' }}
                                        في {{ $activity->date->format('Y/m/d') }}
                                    </p>
                                    <small class="text-muted">
                                        <i class="bi bi-clock me-1"></i>
                                        {{ $activity->created_at->diffForHumans() }}
                                    </small>
                                </div>
                                <span class="badge badge-premium bg-{{ $activity->status === 'present' ? 'success' : 'danger' }}">
                                    {{ $activity->status === 'present' ? 'حاضر' : 'غائب' }}
                                </span>
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-5">
                        <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                            <i class="bi bi-inbox fs-1 text-muted"></i>
                        </div>
                        <h6 class="text-muted">لا توجد أنشطة حديثة</h6>
                        <p class="text-muted small mb-0">ستظهر الأنشطة الجديدة هنا</p>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="premium-card animate-fade-in-up" style="animation-delay: 0.8s;">
            <div class="card-header bg-transparent border-0 py-4">
                <h5 class="mb-1 fw-bold gradient-text">
                    <i class="bi bi-lightning-fill me-2"></i>
                    إجراءات سريعة
                </h5>
                <p class="text-muted mb-0 small">الوصول السريع للمهام الأساسية</p>
            </div>
            <div class="card-body pt-0">
                <div class="d-grid gap-3">
                    <a href="{{ route('employees.create') }}" class="btn btn-premium d-flex align-items-center justify-content-start p-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <div class="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                            <i class="bi bi-person-plus text-white"></i>
                        </div>
                        <div class="text-start">
                            <div class="fw-semibold">إضافة موظف جديد</div>
                            <small class="opacity-75">تسجيل موظف في النظام</small>
                        </div>
                    </a>

                    <a href="{{ route('attendance.index') }}" class="btn btn-premium d-flex align-items-center justify-content-start p-3" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                        <div class="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                            <i class="bi bi-clock-fill text-white"></i>
                        </div>
                        <div class="text-start">
                            <div class="fw-semibold">تسجيل الحضور</div>
                            <small class="opacity-75">إدارة حضور الموظفين</small>
                        </div>
                    </a>

                    <a href="{{ route('leaves.create') }}" class="btn btn-premium d-flex align-items-center justify-content-start p-3" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                        <div class="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                            <i class="bi bi-calendar-plus text-white"></i>
                        </div>
                        <div class="text-start">
                            <div class="fw-semibold">طلب إجازة</div>
                            <small class="opacity-75">تقديم طلب إجازة جديد</small>
                        </div>
                    </a>

                    <a href="{{ route('reports.index') }}" class="btn btn-premium d-flex align-items-center justify-content-start p-3" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                        <div class="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                            <i class="bi bi-graph-up-arrow text-white"></i>
                        </div>
                        <div class="text-start">
                            <div class="fw-semibold">التقارير</div>
                            <small class="opacity-75">عرض التقارير والإحصائيات</small>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Premium Chart Configuration
Chart.defaults.font.family = "'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
Chart.defaults.font.size = 12;
Chart.defaults.color = '#64748b';

// Attendance Chart with Premium Styling
const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
const attendanceChart = new Chart(attendanceCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode(array_column($monthlyAttendance, 'month')) !!},
        datasets: [{
            label: 'حاضر',
            data: {!! json_encode(array_column($monthlyAttendance, 'present')) !!},
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: '#10b981',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8,
            pointHoverBackgroundColor: '#10b981',
            pointHoverBorderColor: '#ffffff',
            pointHoverBorderWidth: 3
        }, {
            label: 'غائب',
            data: {!! json_encode(array_column($monthlyAttendance, 'absent')) !!},
            borderColor: '#ef4444',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: '#ef4444',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8,
            pointHoverBackgroundColor: '#ef4444',
            pointHoverBorderColor: '#ffffff',
            pointHoverBorderWidth: 3
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            intersect: false,
            mode: 'index'
        },
        plugins: {
            legend: {
                display: true,
                position: 'top',
                align: 'end',
                labels: {
                    usePointStyle: true,
                    padding: 20,
                    font: {
                        size: 13,
                        weight: '500'
                    }
                }
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: true,
                padding: 12
            }
        },
        scales: {
            x: {
                grid: {
                    display: false
                },
                border: {
                    display: false
                },
                ticks: {
                    font: {
                        size: 12,
                        weight: '500'
                    },
                    color: '#64748b'
                }
            },
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(148, 163, 184, 0.1)',
                    drawBorder: false
                },
                border: {
                    display: false
                },
                ticks: {
                    font: {
                        size: 12,
                        weight: '500'
                    },
                    color: '#64748b',
                    padding: 10
                }
            }
        }
    }
});

// Department Chart with Premium Styling
const departmentCtx = document.getElementById('departmentChart').getContext('2d');
const departmentChart = new Chart(departmentCtx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode($departmentStats->pluck('name')) !!},
        datasets: [{
            data: {!! json_encode($departmentStats->pluck('employees_count')) !!},
            backgroundColor: [
                '#667eea',
                '#764ba2',
                '#f093fb',
                '#f5576c',
                '#4facfe',
                '#00f2fe'
            ],
            borderWidth: 0,
            hoverBorderWidth: 3,
            hoverBorderColor: '#ffffff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        cutout: '60%',
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                borderWidth: 1,
                cornerRadius: 8,
                padding: 12,
                callbacks: {
                    label: function(context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                        return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                    }
                }
            }
        },
        animation: {
            animateRotate: true,
            animateScale: true,
            duration: 2000,
            easing: 'easeOutQuart'
        }
    }
});

// Premium Auto-refresh with Loading States
let refreshInterval;

function refreshDashboardStats() {
    // Add loading state to cards
    const cards = document.querySelectorAll('.stats-card');
    cards.forEach(card => card.classList.add('loading-pulse'));

    fetch('/dashboard/stats')
        .then(response => response.json())
        .then(data => {
            // Animate number updates
            animateValue('totalEmployees', parseInt(document.getElementById('totalEmployees').textContent), data.totalEmployees, 1000);
            animateValue('todayAttendance', parseInt(document.getElementById('todayAttendance').textContent), data.todayAttendance, 1000);
            animateValue('pendingLeaves', parseInt(document.getElementById('pendingLeaves').textContent), data.pendingLeaves, 1000);

            // Update payroll with formatting
            const currentPayroll = parseFloat(document.getElementById('monthlyPayroll').textContent.replace(/[^\d.-]/g, ''));
            animateValue('monthlyPayroll', currentPayroll, parseFloat(data.monthlyPayroll), 1000);

            // Remove loading state
            setTimeout(() => {
                cards.forEach(card => card.classList.remove('loading-pulse'));
            }, 1000);
        })
        .catch(error => {
            console.error('Error refreshing stats:', error);
            cards.forEach(card => card.classList.remove('loading-pulse'));
        });
}

// Enhanced number animation with easing
function animateValue(id, start, end, duration) {
    const obj = document.getElementById(id);
    const range = end - start;
    const startTime = performance.now();

    function easeOutQuart(t) {
        return 1 - (--t) * t * t * t;
    }

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easedProgress = easeOutQuart(progress);
        const current = Math.round(start + (range * easedProgress));

        if (id === 'monthlyPayroll') {
            obj.textContent = current.toLocaleString('ar-SA');
        } else {
            obj.textContent = current;
        }

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

// Initialize dashboard on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initial number animations
    setTimeout(() => {
        animateValue('totalEmployees', 0, {{ $totalEmployees }}, 2000);
        animateValue('todayAttendance', 0, {{ $todayAttendance }}, 2200);
        animateValue('pendingLeaves', 0, {{ $pendingLeaves }}, 2400);
        animateValue('monthlyPayroll', 0, {{ $monthlyPayroll }}, 2600);
    }, 500);

    // Start auto-refresh every 60 seconds
    refreshInterval = setInterval(refreshDashboardStats, 60000);

    // Add hover effects to stats cards
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Add click effects to quick action buttons
    const actionButtons = document.querySelectorAll('.btn-premium');
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});
</script>
@endpush

@push('styles')
<style>
/* Premium Dashboard Styles */
.activity-timeline {
    position: relative;
}

.activity-item {
    position: relative;
    padding-bottom: 1rem;
}

.activity-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 22px;
    top: 45px;
    bottom: -16px;
    width: 2px;
    background: linear-gradient(to bottom, #e2e8f0, transparent);
}

.activity-icon {
    position: relative;
    z-index: 2;
}

.chart-container {
    position: relative;
}

.chart-container canvas {
    max-height: 300px !important;
}

/* Ripple Effect */
.btn-premium {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Loading States */
.loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .7;
    }
}

/* Enhanced Hover Effects */
.stats-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.premium-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

/* Gradient Text Animation */
.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Floating Animation */
.animate-float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Staggered Animations */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out forwards;
    opacity: 0;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out forwards;
    opacity: 0;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }

    .chart-container {
        height: 250px !important;
    }

    .activity-item {
        padding-bottom: 0.5rem;
    }

    .btn-premium {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}
</style>
@endpush
