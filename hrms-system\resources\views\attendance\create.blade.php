@extends('layouts.app')

@section('title', 'تسجيل حضور جديد')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4 animate-fade-in-down">
    <div>
        <h1 class="h3 mb-2 gradient-text fw-bold">
            <i class="bi bi-plus-circle me-2"></i>
            تسجيل حضور جديد
        </h1>
        <p class="text-muted mb-0">إضافة سجل حضور وانصراف للموظفين</p>
    </div>
    <a href="{{ route('attendance.index') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-right me-2"></i>
        العودة للقائمة
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="premium-card animate-fade-in-up">
            <div class="card-header bg-transparent border-0 py-4">
                <h5 class="mb-0 fw-bold">
                    <i class="bi bi-clock-fill me-2 text-primary"></i>
                    بيانات الحضور والانصراف
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('attendance.store') }}" method="POST" id="attendanceForm">
                    @csrf
                    
                    <div class="row">
                        <!-- اختيار الموظف -->
                        <div class="col-md-6 mb-4">
                            <label for="employee_id" class="form-label fw-semibold">
                                <i class="bi bi-person me-1"></i>
                                الموظف <span class="text-danger">*</span>
                            </label>
                            <select class="form-select select2 @error('employee_id') is-invalid @enderror" 
                                    id="employee_id" 
                                    name="employee_id" 
                                    required>
                                <option value="">اختر الموظف</option>
                                @foreach($employees as $employee)
                                    <option value="{{ $employee->id }}" {{ old('employee_id') == $employee->id ? 'selected' : '' }}>
                                        {{ $employee->full_name }} ({{ $employee->employee_id }})
                                    </option>
                                @endforeach
                            </select>
                            @error('employee_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- التاريخ -->
                        <div class="col-md-6 mb-4">
                            <label for="date" class="form-label fw-semibold">
                                <i class="bi bi-calendar me-1"></i>
                                التاريخ <span class="text-danger">*</span>
                            </label>
                            <input type="date" 
                                   class="form-control @error('date') is-invalid @enderror" 
                                   id="date" 
                                   name="date" 
                                   value="{{ old('date', date('Y-m-d')) }}" 
                                   required>
                            @error('date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <!-- وقت الدخول -->
                        <div class="col-md-6 mb-4">
                            <label for="check_in" class="form-label fw-semibold">
                                <i class="bi bi-box-arrow-in-right me-1 text-success"></i>
                                وقت الدخول
                            </label>
                            <input type="time" 
                                   class="form-control @error('check_in') is-invalid @enderror" 
                                   id="check_in" 
                                   name="check_in" 
                                   value="{{ old('check_in') }}">
                            @error('check_in')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- وقت الخروج -->
                        <div class="col-md-6 mb-4">
                            <label for="check_out" class="form-label fw-semibold">
                                <i class="bi bi-box-arrow-right me-1 text-info"></i>
                                وقت الخروج
                            </label>
                            <input type="time" 
                                   class="form-control @error('check_out') is-invalid @enderror" 
                                   id="check_out" 
                                   name="check_out" 
                                   value="{{ old('check_out') }}">
                            @error('check_out')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <!-- الحالة -->
                        <div class="col-md-6 mb-4">
                            <label for="status" class="form-label fw-semibold">
                                <i class="bi bi-flag me-1"></i>
                                الحالة <span class="text-danger">*</span>
                            </label>
                            <select class="form-select @error('status') is-invalid @enderror" 
                                    id="status" 
                                    name="status" 
                                    required>
                                <option value="">اختر الحالة</option>
                                <option value="present" {{ old('status') == 'present' ? 'selected' : '' }}>حاضر</option>
                                <option value="absent" {{ old('status') == 'absent' ? 'selected' : '' }}>غائب</option>
                                <option value="late" {{ old('status') == 'late' ? 'selected' : '' }}>متأخر</option>
                                <option value="half_day" {{ old('status') == 'half_day' ? 'selected' : '' }}>نصف يوم</option>
                                <option value="holiday" {{ old('status') == 'holiday' ? 'selected' : '' }}>عطلة</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- إجمالي الساعات -->
                        <div class="col-md-6 mb-4">
                            <label for="total_hours" class="form-label fw-semibold">
                                <i class="bi bi-hourglass-split me-1"></i>
                                إجمالي الساعات
                            </label>
                            <input type="number" 
                                   class="form-control @error('total_hours') is-invalid @enderror" 
                                   id="total_hours" 
                                   name="total_hours" 
                                   step="0.5" 
                                   min="0" 
                                   max="24" 
                                   value="{{ old('total_hours') }}" 
                                   readonly>
                            @error('total_hours')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">سيتم حساب الساعات تلقائياً عند إدخال أوقات الدخول والخروج</div>
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    <div class="mb-4">
                        <label for="notes" class="form-label fw-semibold">
                            <i class="bi bi-chat-text me-1"></i>
                            ملاحظات
                        </label>
                        <textarea class="form-control @error('notes') is-invalid @enderror" 
                                  id="notes" 
                                  name="notes" 
                                  rows="3" 
                                  placeholder="أدخل أي ملاحظات إضافية...">{{ old('notes') }}</textarea>
                        @error('notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                            <i class="bi bi-x-circle me-2"></i>
                            إلغاء
                        </button>
                        <div>
                            <button type="reset" class="btn btn-outline-warning me-2">
                                <i class="bi bi-arrow-clockwise me-2"></i>
                                إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-premium">
                                <i class="bi bi-check-circle me-2"></i>
                                حفظ السجل
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap-5',
        placeholder: 'اختر الموظف',
        allowClear: true
    });

    // Calculate total hours automatically
    function calculateHours() {
        const checkIn = $('#check_in').val();
        const checkOut = $('#check_out').val();
        
        if (checkIn && checkOut) {
            const checkInTime = new Date('2000-01-01 ' + checkIn);
            const checkOutTime = new Date('2000-01-01 ' + checkOut);
            
            if (checkOutTime > checkInTime) {
                const diffMs = checkOutTime - checkInTime;
                const diffHours = diffMs / (1000 * 60 * 60);
                $('#total_hours').val(diffHours.toFixed(1));
            }
        }
    }

    // Auto-calculate hours when times change
    $('#check_in, #check_out').on('change', calculateHours);

    // Auto-set status based on check-in time
    $('#check_in').on('change', function() {
        const checkInTime = $(this).val();
        if (checkInTime) {
            const checkInHour = parseInt(checkInTime.split(':')[0]);
            const checkInMinute = parseInt(checkInTime.split(':')[1]);
            const totalMinutes = checkInHour * 60 + checkInMinute;
            const workStartMinutes = 8 * 60; // 8:00 AM
            const lateThreshold = workStartMinutes + 15; // 8:15 AM
            
            if (totalMinutes <= workStartMinutes) {
                $('#status').val('present');
            } else if (totalMinutes <= lateThreshold) {
                $('#status').val('late');
            }
        }
    });

    // Form validation
    $('#attendanceForm').on('submit', function(e) {
        const employeeId = $('#employee_id').val();
        const date = $('#date').val();
        const status = $('#status').val();
        
        if (!employeeId || !date || !status) {
            e.preventDefault();
            Swal.fire({
                title: 'خطأ في البيانات',
                text: 'يرجى ملء جميع الحقول المطلوبة',
                icon: 'error',
                confirmButtonText: 'موافق'
            });
            return false;
        }
        
        // Show loading
        Swal.fire({
            title: 'جاري الحفظ...',
            text: 'يرجى الانتظار',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    });
});
</script>
@endpush

@push('styles')
<style>
.premium-card {
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
    border-radius: 15px;
}

.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-premium {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 25px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-premium:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.select2-container--bootstrap-5 .select2-selection {
    border-color: #dee2e6;
}

.select2-container--bootstrap-5 .select2-selection:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}
</style>
@endpush
