# ⚡ إعداد سريع لقاعدة بيانات MySQL

## 🎯 خطوات سريعة (5 دقائق)

### 1. تشغيل XAMPP
- افتح XAMPP Control Panel
- تأكد من تشغيل **Apache** و **MySQL**

### 2. إنشاء قاعدة البيانات
- افتح phpMyAdmin: `http://localhost/phpmyadmin`
- انقر "New" → اكتب `hrms_system` → انقر "Create"

### 3. تشغيل الأوامر في Terminal/CMD

```bash
# انتقل لمجلد المشروع
cd hrms-system

# مسح الكاش
php artisan config:clear

# تشغيل الهجرات
php artisan migrate

# إضافة البيانات التجريبية
php artisan db:seed

# تشغيل النظام
php artisan serve
```

### 4. فتح النظام
- افتح المتصفح: `http://localhost:8000`
- سجل الدخول بـ: `<EMAIL>` / `123456`

## ✅ تم الانتهاء!

النظام الآن يعمل مع MySQL في XAMPP.

---

## 🔧 في حالة وجود مشاكل:

### مشكلة: قاعدة البيانات غير موجودة
```sql
-- في phpMyAdmin، شغل هذا الكود:
CREATE DATABASE hrms_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### مشكلة: خطأ في الاتصال
- تأكد من تشغيل MySQL في XAMPP
- تحقق من ملف `.env` أن الإعدادات صحيحة:
```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hrms_system
DB_USERNAME=root
DB_PASSWORD=
```

### مشكلة: الجداول غير موجودة
```bash
php artisan migrate:fresh --seed
```

---

**🎉 مبروك! النظام جاهز للاستخدام مع MySQL**
