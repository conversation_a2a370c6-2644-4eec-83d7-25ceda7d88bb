@extends('layouts.app')

@section('title', 'الإعدادات')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4 animate-fade-in-down">
    <div>
        <h1 class="h3 mb-2 gradient-text fw-bold">
            <i class="bi bi-gear-fill me-2"></i>
            الإعدادات
        </h1>
        <p class="text-muted mb-0">إدارة إعدادات النظام والملف الشخصي</p>
    </div>
    <div class="btn-group">
        <button type="button" class="btn btn-outline-info" onclick="getSystemInfo()">
            <i class="bi bi-info-circle me-2"></i>
            معلومات النظام
        </button>
        <button type="button" class="btn btn-outline-warning" onclick="clearCache()">
            <i class="bi bi-arrow-clockwise me-2"></i>
            مسح الذاكرة المؤقتة
        </button>
    </div>
</div>

<div class="row">
    <!-- Settings Navigation -->
    <div class="col-lg-3 mb-4">
        <div class="premium-card animate-fade-in-up">
            <div class="card-body p-0">
                <div class="nav flex-column nav-pills" id="settings-tab" role="tablist">
                    <button class="nav-link active" id="profile-tab" data-bs-toggle="pill" data-bs-target="#profile" type="button" role="tab">
                        <i class="bi bi-person-circle me-2"></i>
                        الملف الشخصي
                    </button>
                    <button class="nav-link" id="password-tab" data-bs-toggle="pill" data-bs-target="#password" type="button" role="tab">
                        <i class="bi bi-shield-lock me-2"></i>
                        كلمة المرور
                    </button>
                    <button class="nav-link" id="system-tab" data-bs-toggle="pill" data-bs-target="#system" type="button" role="tab">
                        <i class="bi bi-gear me-2"></i>
                        إعدادات النظام
                    </button>
                    <button class="nav-link" id="notifications-tab" data-bs-toggle="pill" data-bs-target="#notifications" type="button" role="tab">
                        <i class="bi bi-bell me-2"></i>
                        الإشعارات
                    </button>
                    <button class="nav-link" id="backup-tab" data-bs-toggle="pill" data-bs-target="#backup" type="button" role="tab">
                        <i class="bi bi-cloud-download me-2"></i>
                        النسخ الاحتياطي
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Content -->
    <div class="col-lg-9">
        <div class="tab-content" id="settings-tabContent">
            <!-- Profile Settings -->
            <div class="tab-pane fade show active" id="profile" role="tabpanel">
                <div class="premium-card animate-fade-in-up">
                    <div class="card-header bg-transparent border-0 py-4">
                        <h5 class="mb-0 fw-bold">
                            <i class="bi bi-person-circle me-2 text-primary"></i>
                            الملف الشخصي
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('settings.profile') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="row">
                                <div class="col-md-4 text-center mb-4">
                                    <div class="avatar-upload">
                                        <div class="avatar-preview">
                                            <div id="imagePreview" style="background-image: url('{{ $user->avatar ? Storage::url($user->avatar) : asset('assets/images/default-avatar.png') }}');">
                                            </div>
                                        </div>
                                        <div class="avatar-edit">
                                            <input type="file" id="imageUpload" name="avatar" accept=".png, .jpg, .jpeg" />
                                            <label for="imageUpload">
                                                <i class="bi bi-camera-fill"></i>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <label for="name" class="form-label fw-semibold">الاسم الكامل</label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                   id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-md-12 mb-3">
                                            <label for="email" class="form-label fw-semibold">البريد الإلكتروني</label>
                                            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                                   id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                            @error('email')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-md-12 mb-3">
                                            <label for="phone" class="form-label fw-semibold">رقم الهاتف</label>
                                            <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                                   id="phone" name="phone" value="{{ old('phone', $user->phone) }}">
                                            @error('phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-end">
                                <button type="submit" class="btn btn-premium">
                                    <i class="bi bi-check-circle me-2"></i>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Password Settings -->
            <div class="tab-pane fade" id="password" role="tabpanel">
                <div class="premium-card animate-fade-in-up">
                    <div class="card-header bg-transparent border-0 py-4">
                        <h5 class="mb-0 fw-bold">
                            <i class="bi bi-shield-lock me-2 text-warning"></i>
                            تغيير كلمة المرور
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('settings.password') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="current_password" class="form-label fw-semibold">كلمة المرور الحالية</label>
                                    <input type="password" class="form-control @error('current_password') is-invalid @enderror" 
                                           id="current_password" name="current_password" required>
                                    @error('current_password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label fw-semibold">كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                           id="password" name="password" required>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="password_confirmation" class="form-label fw-semibold">تأكيد كلمة المرور</label>
                                    <input type="password" class="form-control" 
                                           id="password_confirmation" name="password_confirmation" required>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل
                            </div>
                            <div class="text-end">
                                <button type="submit" class="btn btn-warning">
                                    <i class="bi bi-shield-check me-2"></i>
                                    تحديث كلمة المرور
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- System Settings -->
            <div class="tab-pane fade" id="system" role="tabpanel">
                <div class="premium-card animate-fade-in-up">
                    <div class="card-header bg-transparent border-0 py-4">
                        <h5 class="mb-0 fw-bold">
                            <i class="bi bi-gear me-2 text-info"></i>
                            إعدادات النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('settings.system') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="company_name" class="form-label fw-semibold">اسم الشركة</label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" 
                                           value="{{ $systemSettings['company_name'] }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="company_email" class="form-label fw-semibold">بريد الشركة</label>
                                    <input type="email" class="form-control" id="company_email" name="company_email" 
                                           value="{{ $systemSettings['company_email'] }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="company_phone" class="form-label fw-semibold">هاتف الشركة</label>
                                    <input type="text" class="form-control" id="company_phone" name="company_phone" 
                                           value="{{ $systemSettings['company_phone'] }}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="timezone" class="form-label fw-semibold">المنطقة الزمنية</label>
                                    <select class="form-select" id="timezone" name="timezone">
                                        <option value="Asia/Riyadh" {{ $systemSettings['timezone'] == 'Asia/Riyadh' ? 'selected' : '' }}>الرياض</option>
                                        <option value="Asia/Dubai" {{ $systemSettings['timezone'] == 'Asia/Dubai' ? 'selected' : '' }}>دبي</option>
                                        <option value="Asia/Kuwait" {{ $systemSettings['timezone'] == 'Asia/Kuwait' ? 'selected' : '' }}>الكويت</option>
                                    </select>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="company_address" class="form-label fw-semibold">عنوان الشركة</label>
                                    <textarea class="form-control" id="company_address" name="company_address" rows="3">{{ $systemSettings['company_address'] }}</textarea>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="working_hours_start" class="form-label fw-semibold">بداية الدوام</label>
                                    <input type="time" class="form-control" id="working_hours_start" name="working_hours_start" 
                                           value="{{ $systemSettings['working_hours_start'] }}">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="working_hours_end" class="form-label fw-semibold">نهاية الدوام</label>
                                    <input type="time" class="form-control" id="working_hours_end" name="working_hours_end" 
                                           value="{{ $systemSettings['working_hours_end'] }}">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="currency" class="form-label fw-semibold">العملة</label>
                                    <select class="form-select" id="currency" name="currency">
                                        <option value="SAR" {{ $systemSettings['currency'] == 'SAR' ? 'selected' : '' }}>ريال سعودي</option>
                                        <option value="AED" {{ $systemSettings['currency'] == 'AED' ? 'selected' : '' }}>درهم إماراتي</option>
                                        <option value="KWD" {{ $systemSettings['currency'] == 'KWD' ? 'selected' : '' }}>دينار كويتي</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="annual_leave_days" class="form-label fw-semibold">أيام الإجازة السنوية</label>
                                    <input type="number" class="form-control" id="annual_leave_days" name="annual_leave_days" 
                                           value="{{ $systemSettings['annual_leave_days'] }}" min="1" max="365">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="sick_leave_days" class="form-label fw-semibold">أيام الإجازة المرضية</label>
                                    <input type="number" class="form-control" id="sick_leave_days" name="sick_leave_days" 
                                           value="{{ $systemSettings['sick_leave_days'] }}" min="1" max="365">
                                </div>
                            </div>
                            <div class="text-end">
                                <button type="submit" class="btn btn-info">
                                    <i class="bi bi-gear-fill me-2"></i>
                                    حفظ الإعدادات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Notifications Settings -->
            <div class="tab-pane fade" id="notifications" role="tabpanel">
                <div class="premium-card animate-fade-in-up">
                    <div class="card-header bg-transparent border-0 py-4">
                        <h5 class="mb-0 fw-bold">
                            <i class="bi bi-bell me-2 text-success"></i>
                            إعدادات الإشعارات
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('settings.notifications') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" value="1" checked>
                                        <label class="form-check-label fw-semibold" for="email_notifications">
                                            إشعارات البريد الإلكتروني
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="sms_notifications" name="sms_notifications" value="1">
                                        <label class="form-check-label fw-semibold" for="sms_notifications">
                                            إشعارات الرسائل النصية
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="leave_requests" name="leave_requests" value="1" checked>
                                        <label class="form-check-label fw-semibold" for="leave_requests">
                                            طلبات الإجازات
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="attendance_alerts" name="attendance_alerts" value="1" checked>
                                        <label class="form-check-label fw-semibold" for="attendance_alerts">
                                            تنبيهات الحضور
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="payroll_notifications" name="payroll_notifications" value="1" checked>
                                        <label class="form-check-label fw-semibold" for="payroll_notifications">
                                            إشعارات المرتبات
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="system_updates" name="system_updates" value="1" checked>
                                        <label class="form-check-label fw-semibold" for="system_updates">
                                            تحديثات النظام
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="text-end">
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-bell-fill me-2"></i>
                                    حفظ إعدادات الإشعارات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Backup Settings -->
            <div class="tab-pane fade" id="backup" role="tabpanel">
                <div class="premium-card animate-fade-in-up">
                    <div class="card-header bg-transparent border-0 py-4">
                        <h5 class="mb-0 fw-bold">
                            <i class="bi bi-cloud-download me-2 text-danger"></i>
                            النسخ الاحتياطي والصيانة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="bi bi-database text-primary" style="font-size: 3rem;"></i>
                                        <h6 class="mt-3 mb-3">نسخة احتياطية من البيانات</h6>
                                        <button type="button" class="btn btn-outline-primary" onclick="createBackup()">
                                            <i class="bi bi-download me-2"></i>
                                            إنشاء نسخة احتياطية
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="bi bi-arrow-clockwise text-warning" style="font-size: 3rem;"></i>
                                        <h6 class="mt-3 mb-3">مسح الذاكرة المؤقتة</h6>
                                        <button type="button" class="btn btn-outline-warning" onclick="clearCache()">
                                            <i class="bi bi-trash me-2"></i>
                                            مسح الذاكرة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="mb-3">اختبار البريد الإلكتروني</h6>
                                        <div class="input-group">
                                            <input type="email" class="form-control" id="test_email" placeholder="أدخل البريد الإلكتروني للاختبار">
                                            <button class="btn btn-outline-info" type="button" onclick="testEmail()">
                                                <i class="bi bi-envelope me-2"></i>
                                                إرسال اختبار
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Avatar upload preview
    $("#imageUpload").change(function() {
        readURL(this);
    });

    function readURL(input) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#imagePreview').css('background-image', 'url('+e.target.result +')');
                $('#imagePreview').hide();
                $('#imagePreview').fadeIn(650);
            }
            reader.readAsDataURL(input.files[0]);
        }
    }
});

// Create Backup Function
function createBackup() {
    Swal.fire({
        title: 'إنشاء نسخة احتياطية',
        text: 'هل أنت متأكد من إنشاء نسخة احتياطية من البيانات؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#007bff',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، أنشئ',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('{{ route("settings.backup") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم!', data.message, 'success');
                } else {
                    Swal.fire('خطأ!', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.fire('خطأ!', 'حدث خطأ في الشبكة', 'error');
            });
        }
    });
}

// Clear Cache Function
function clearCache() {
    Swal.fire({
        title: 'مسح الذاكرة المؤقتة',
        text: 'هل أنت متأكد من مسح الذاكرة المؤقتة؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ffc107',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، امسح',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('{{ route("settings.clear-cache") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('تم!', data.message, 'success');
                } else {
                    Swal.fire('خطأ!', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.fire('خطأ!', 'حدث خطأ في الشبكة', 'error');
            });
        }
    });
}

// Test Email Function
function testEmail() {
    const email = document.getElementById('test_email').value;

    if (!email) {
        Swal.fire('خطأ!', 'يرجى إدخال بريد إلكتروني صالح', 'error');
        return;
    }

    fetch('{{ route("settings.test-email") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            test_email: email
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire('تم!', data.message, 'success');
        } else {
            Swal.fire('خطأ!', data.message, 'error');
        }
    })
    .catch(error => {
        Swal.fire('خطأ!', 'حدث خطأ في الشبكة', 'error');
    });
}

// Get System Info Function
function getSystemInfo() {
    fetch('{{ route("settings.system-info") }}')
    .then(response => response.json())
    .then(data => {
        let infoHtml = `
            <div class="text-start">
                <p><strong>إصدار PHP:</strong> ${data.php_version}</p>
                <p><strong>إصدار Laravel:</strong> ${data.laravel_version}</p>
                <p><strong>خادم الويب:</strong> ${data.server_software}</p>
                <p><strong>إصدار قاعدة البيانات:</strong> ${data.database_version}</p>
                <p><strong>حد الذاكرة:</strong> ${data.memory_limit}</p>
                <p><strong>حد وقت التنفيذ:</strong> ${data.max_execution_time} ثانية</p>
                <p><strong>حد رفع الملفات:</strong> ${data.upload_max_filesize}</p>
            </div>
        `;

        Swal.fire({
            title: 'معلومات النظام',
            html: infoHtml,
            icon: 'info',
            confirmButtonText: 'موافق',
            width: '600px'
        });
    })
    .catch(error => {
        Swal.fire('خطأ!', 'لا يمكن الحصول على معلومات النظام', 'error');
    });
}
</script>
@endpush

@push('styles')
<style>
.premium-card {
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
    border-radius: 15px;
}

.nav-pills .nav-link {
    border-radius: 0;
    border: none;
    color: #6c757d;
    padding: 15px 20px;
    margin: 0;
    text-align: right;
    transition: all 0.3s ease;
}

.nav-pills .nav-link:first-child {
    border-radius: 15px 15px 0 0;
}

.nav-pills .nav-link:last-child {
    border-radius: 0 0 15px 15px;
}

.nav-pills .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.nav-pills .nav-link:hover:not(.active) {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.avatar-upload {
    position: relative;
    max-width: 150px;
    margin: 0 auto;
}

.avatar-upload .avatar-edit {
    position: absolute;
    right: 12px;
    z-index: 1;
    top: 10px;
}

.avatar-upload .avatar-edit input {
    display: none;
}

.avatar-upload .avatar-edit input + label {
    display: inline-block;
    width: 34px;
    height: 34px;
    margin-bottom: 0;
    border-radius: 100%;
    background: #FFFFFF;
    border: 1px solid transparent;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.12);
    cursor: pointer;
    font-weight: normal;
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-upload .avatar-edit input + label:hover {
    background: #f1f1f1;
    border-color: #d6d6d6;
}

.avatar-upload .avatar-preview {
    width: 150px;
    height: 150px;
    position: relative;
    border-radius: 100%;
    border: 6px solid #F8F8F8;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
}

.avatar-upload .avatar-preview > div {
    width: 100%;
    height: 100%;
    border-radius: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.btn-premium {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 25px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-premium:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}
</style>
@endpush
