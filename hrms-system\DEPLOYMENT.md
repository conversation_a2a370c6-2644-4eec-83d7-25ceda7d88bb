# 🚀 دليل النشر - نظام إدارة الموارد البشرية

## متطلبات الخادم

### الحد الأدنى:
- **نظام التشغيل**: Linux (Ubuntu 20.04+ موصى به)
- **خادم الويب**: Apache 2.4+ أو Nginx 1.18+
- **PHP**: 8.2+ مع الإضافات المطلوبة
- **قاعدة البيانات**: MySQL 8.0+ أو MariaDB 10.5+
- **الذاكرة**: 2GB RAM
- **مساحة القرص**: 5GB

### إضافات PHP المطلوبة:
```bash
sudo apt install php8.2-cli php8.2-fpm php8.2-mysql php8.2-xml php8.2-curl php8.2-gd php8.2-mbstring php8.2-zip php8.2-bcmath php8.2-intl
```

## خطوات النشر

### 1. تحضير الخادم

```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Apache/Nginx و MySQL
sudo apt install apache2 mysql-server -y

# تثبيت PHP و Composer
sudo apt install php8.2 composer -y

# تثبيت Node.js (اختياري)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs -y
```

### 2. إعداد قاعدة البيانات

```bash
# الدخول إلى MySQL
sudo mysql -u root -p

# إنشاء قاعدة البيانات والمستخدم
CREATE DATABASE hrms_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'hrms_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON hrms_production.* TO 'hrms_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. رفع الملفات

```bash
# إنشاء مجلد التطبيق
sudo mkdir -p /var/www/hrms
sudo chown -R $USER:www-data /var/www/hrms

# رفع الملفات (استخدم Git أو FTP)
cd /var/www/hrms
git clone https://github.com/your-repo/hrms-system.git .

# أو رفع الملفات يدوياً
# scp -r hrms-system/* user@server:/var/www/hrms/
```

### 4. تكوين التطبيق

```bash
# تثبيت التبعيات
composer install --optimize-autoloader --no-dev

# نسخ ملف البيئة
cp .env.production .env

# تحديث إعدادات قاعدة البيانات في .env
nano .env

# توليد مفتاح التطبيق
php artisan key:generate

# تشغيل الهجرات
php artisan migrate --force

# إضافة البيانات الأساسية
php artisan db:seed --force

# إنشاء رابط التخزين
php artisan storage:link

# تحسين الأداء
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 5. تكوين Apache

```bash
# إنشاء ملف التكوين
sudo nano /etc/apache2/sites-available/hrms.conf
```

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    ServerAlias www.your-domain.com
    DocumentRoot /var/www/hrms/public
    
    <Directory /var/www/hrms/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/hrms_error.log
    CustomLog ${APACHE_LOG_DIR}/hrms_access.log combined
</VirtualHost>
```

```bash
# تفعيل الموقع
sudo a2ensite hrms.conf
sudo a2enmod rewrite
sudo systemctl reload apache2
```

### 6. تكوين SSL (HTTPS)

```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-apache -y

# الحصول على شهادة SSL
sudo certbot --apache -d your-domain.com -d www.your-domain.com

# تجديد تلقائي
sudo crontab -e
# إضافة السطر التالي:
0 12 * * * /usr/bin/certbot renew --quiet
```

### 7. تكوين الصلاحيات

```bash
# تعيين الصلاحيات الصحيحة
sudo chown -R www-data:www-data /var/www/hrms
sudo chmod -R 755 /var/www/hrms
sudo chmod -R 775 /var/www/hrms/storage
sudo chmod -R 775 /var/www/hrms/bootstrap/cache
```

### 8. إعداد Cron Jobs

```bash
# تحرير crontab
sudo crontab -e

# إضافة المهام المجدولة
* * * * * cd /var/www/hrms && php artisan schedule:run >> /dev/null 2>&1
0 2 * * * cd /var/www/hrms && php artisan backup:run
0 3 * * 0 cd /var/www/hrms && php artisan backup:clean
```

## تكوين Nginx (بديل لـ Apache)

```bash
# إنشاء ملف التكوين
sudo nano /etc/nginx/sites-available/hrms
```

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    root /var/www/hrms/public;
    index index.php index.html;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }

    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

```bash
# تفعيل الموقع
sudo ln -s /etc/nginx/sites-available/hrms /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## المراقبة والصيانة

### 1. مراقبة السجلات

```bash
# سجلات Laravel
tail -f /var/www/hrms/storage/logs/laravel.log

# سجلات Apache
tail -f /var/log/apache2/hrms_error.log

# سجلات النظام
tail -f /var/log/syslog
```

### 2. النسخ الاحتياطي

```bash
# نسخ احتياطي يدوي
cd /var/www/hrms
php artisan backup:run

# نسخ احتياطي لقاعدة البيانات فقط
mysqldump -u hrms_user -p hrms_production > backup_$(date +%Y%m%d).sql
```

### 3. التحديثات

```bash
# سحب آخر التحديثات
cd /var/www/hrms
git pull origin main

# تحديث التبعيات
composer install --optimize-autoloader --no-dev

# تشغيل الهجرات الجديدة
php artisan migrate --force

# مسح الكاش
php artisan cache:clear
php artisan config:cache
php artisan route:cache
php artisan view:cache

# إعادة تشغيل الخدمات
sudo systemctl reload apache2
# أو
sudo systemctl reload nginx
```

## الأمان

### 1. جدار الحماية

```bash
# تفعيل UFW
sudo ufw enable

# السماح بالخدمات الأساسية
sudo ufw allow ssh
sudo ufw allow 'Apache Full'
# أو
sudo ufw allow 'Nginx Full'

# حظر الوصول المباشر لـ PHP
sudo ufw deny 9000
```

### 2. تحديثات الأمان

```bash
# تحديثات تلقائية
sudo apt install unattended-upgrades -y
sudo dpkg-reconfigure -plow unattended-upgrades
```

### 3. مراقبة الأمان

```bash
# تثبيت Fail2Ban
sudo apt install fail2ban -y

# تكوين Fail2Ban للـ Laravel
sudo nano /etc/fail2ban/jail.local
```

```ini
[laravel]
enabled = true
port = http,https
filter = laravel
logpath = /var/www/hrms/storage/logs/laravel.log
maxretry = 3
bantime = 3600
```

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ 500**: تحقق من سجلات الأخطاء وصلاحيات الملفات
2. **خطأ قاعدة البيانات**: تحقق من إعدادات الاتصال في .env
3. **ملفات CSS/JS لا تعمل**: تأكد من تشغيل `php artisan storage:link`
4. **بطء في الأداء**: فعّل OPcache وتحسين قاعدة البيانات

### أوامر مفيدة:

```bash
# فحص حالة التطبيق
php artisan about

# فحص التكوين
php artisan config:show

# مسح جميع أنواع الكاش
php artisan optimize:clear

# إعادة تحسين التطبيق
php artisan optimize
```

---

**تم إعداد هذا الدليل لضمان نشر آمن وموثوق للنظام** 🔒
