<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Employee;
use App\Models\Department;
use App\Models\Attendance;
use App\Models\Leave;
use App\Models\Payroll;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the dashboard.
     */
    public function index()
    {
        // Get statistics
        $totalEmployees = Employee::where('employment_status', 'active')->count();
        $totalDepartments = Department::where('is_active', true)->count();

        // Today's attendance
        $todayAttendance = Attendance::whereDate('date', today())
            ->where('status', 'present')
            ->count();

        // Pending leaves
        $pendingLeaves = Leave::where('status', 'pending')->count();

        // This month's payroll
        $currentMonth = Carbon::now()->format('Y-m');
        $monthlyPayroll = Payroll::where('payroll_period', $currentMonth)
            ->sum('net_salary');

        // Recent activities (last 10 attendance records)
        $recentActivities = Attendance::with('employee')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Department wise employee count
        $departmentStats = Department::withCount('employees')
            ->where('is_active', true)
            ->get();

        // Monthly attendance chart data
        $monthlyAttendance = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $monthlyAttendance[] = [
                'month' => $month->format('M Y'),
                'present' => Attendance::whereYear('date', $month->year)
                    ->whereMonth('date', $month->month)
                    ->where('status', 'present')
                    ->count(),
                'absent' => Attendance::whereYear('date', $month->year)
                    ->whereMonth('date', $month->month)
                    ->where('status', 'absent')
                    ->count(),
            ];
        }

        return view('dashboard.index', compact(
            'totalEmployees',
            'totalDepartments',
            'todayAttendance',
            'pendingLeaves',
            'monthlyPayroll',
            'recentActivities',
            'departmentStats',
            'monthlyAttendance'
        ));
    }

    /**
     * Get dashboard statistics for AJAX requests.
     */
    public function getStats()
    {
        $totalEmployees = Employee::where('employment_status', 'active')->count();
        $todayAttendance = Attendance::whereDate('date', today())
            ->where('status', 'present')
            ->count();
        $pendingLeaves = Leave::where('status', 'pending')->count();
        $currentMonth = Carbon::now()->format('Y-m');
        $monthlyPayroll = Payroll::where('payroll_period', $currentMonth)
            ->sum('net_salary');

        return response()->json([
            'totalEmployees' => $totalEmployees,
            'todayAttendance' => $todayAttendance,
            'pendingLeaves' => $pendingLeaves,
            'monthlyPayroll' => number_format($monthlyPayroll, 2)
        ]);
    }
}
