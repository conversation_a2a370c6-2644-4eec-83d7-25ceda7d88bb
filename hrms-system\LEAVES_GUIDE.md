# 📅 دليل نظام إدارة الإجازات

## 🎯 نظرة عامة

تم تطوير نظام إدارة الإجازات ليكون شاملاً ومتطوراً مع واجهة مستخدم احترافية وميزات متقدمة.

## ✨ الميزات المضافة

### 🎨 **التصميم والواجهة:**
- **تصميم احترافي** مع Glass Morphism وتدرجات لونية
- **بطاقات إحصائيات تفاعلية** مع تأثيرات hover متقدمة
- **رسوم متحركة سلسة** للعناصر والانتقالات
- **تصميم متجاوب** يعمل على جميع الأجهزة

### 📋 **إدارة الطلبات:**
- **أنواع إجازات متعددة:**
  - إجازة سنوية
  - إجازة مرضية
  - إجازة طارئة
  - إجازة أمومة
  - إجازة أبوة
  - إجازة بدون راتب

- **إجازات نصف يوم** مع تحديد الفترة (صباحية/مسائية)
- **حساب تلقائي** لعدد الأيام
- **رفع مرفقات** (PDF, Word, صور)
- **تتبع حالة الطلب** (معلق، موافق عليه، مرفوض)

### 🔍 **البحث والتصفية:**
- **تصفية متقدمة** حسب الحالة ونوع الإجازة والموظف
- **بحث سريع** في جميع الطلبات
- **جدول تفاعلي** مع ترتيب وتصفية
- **تصدير البيانات** (Excel, PDF, طباعة)

### ⚡ **الإجراءات السريعة:**
- **موافقة/رفض فردي** مع ملاحظات
- **موافقة جماعية** للطلبات المحددة
- **عرض تفاصيل شامل** لكل طلب
- **تعديل الطلبات المعلقة**

### 📊 **الإحصائيات والتقارير:**
- **إحصائيات شاملة** للطلبات
- **رصيد الإجازات** لكل موظف
- **إحصائيات شهرية** للموظفين
- **تقارير مرئية** بالمخططات

## 🗂️ الملفات المضافة

### Views (ملفات العرض):
```
resources/views/leaves/
├── index.blade.php      # قائمة الإجازات الرئيسية
├── create.blade.php     # نموذج طلب إجازة جديد
├── show.blade.php       # عرض تفاصيل الطلب
└── edit.blade.php       # تعديل طلب الإجازة
```

### Controller (المتحكم):
```
app/Http/Controllers/LeaveController.php
├── index()              # عرض قائمة الإجازات مع التصفية
├── create()             # عرض نموذج الإنشاء
├── store()              # حفظ طلب جديد
├── show()               # عرض تفاصيل الطلب
├── edit()               # عرض نموذج التعديل
├── update()             # تحديث الطلب
├── destroy()            # حذف الطلب
├── approve()            # الموافقة على الطلب
├── reject()             # رفض الطلب
└── getLeaveBalance()    # الحصول على رصيد الإجازات
```

### Styles (الأنماط):
```
public/assets/css/leaves-style.css
├── Stats Cards Styling
├── Leave Type Badges
├── Status Badges
├── Form Enhancements
├── Calendar Styling
├── Timeline Styles
└── Responsive Design
```

## 🎛️ الوظائف المتقدمة

### 1. **نظام الموافقات:**
- موافقة فردية مع ملاحظات
- رفض مع سبب إجباري
- موافقة جماعية للطلبات المتعددة
- تتبع من وافق ومتى

### 2. **إدارة المرفقات:**
- رفع ملفات متعددة الأنواع
- معاينة المرفقات
- حذف واستبدال المرفقات
- تخزين آمن للملفات

### 3. **حساب رصيد الإجازات:**
- حساب تلقائي للرصيد المتبقي
- تتبع الإجازات المستخدمة
- عرض الاستحقاق السنوي
- تحديث فوري للأرصدة

### 4. **التحقق من صحة البيانات:**
- تحقق من التواريخ
- منع التداخل في الإجازات
- التأكد من توفر الرصيد
- رسائل خطأ واضحة

## 🔧 الإعدادات والتخصيص

### إعدادات الإجازات:
```php
// في LeaveController
$annualEntitlement = 21; // أيام الإجازة السنوية
$sickEntitlement = 30;   // أيام الإجازة المرضية
```

### أنواع الإجازات المدعومة:
- `annual` - إجازة سنوية
- `sick` - إجازة مرضية
- `emergency` - إجازة طارئة
- `maternity` - إجازة أمومة
- `paternity` - إجازة أبوة
- `unpaid` - إجازة بدون راتب

### حالات الطلبات:
- `pending` - معلق (افتراضي)
- `approved` - موافق عليه
- `rejected` - مرفوض

## 📱 واجهة المستخدم

### الصفحة الرئيسية:
- **بطاقات إحصائيات** ملونة ومتحركة
- **جدول تفاعلي** مع البحث والتصفية
- **أزرار إجراءات** سريعة
- **تصدير البيانات** بصيغ متعددة

### نموذج الطلب:
- **اختيار الموظف** مع بحث
- **تحديد نوع الإجازة** بأيقونات
- **تواريخ تفاعلية** مع التحقق
- **حساب تلقائي** للأيام
- **رفع مرفقات** بسهولة

### صفحة التفاصيل:
- **معلومات شاملة** منظمة
- **إحصائيات الموظف** الشهرية
- **رصيد الإجازات** المتبقي
- **إجراءات سريعة** للموافقة/الرفض

## 🚀 الميزات التقنية

### الأمان:
- **تحقق من الصلاحيات** لكل إجراء
- **حماية CSRF** لجميع النماذج
- **تشفير الملفات** المرفوعة
- **تسجيل العمليات** للمراجعة

### الأداء:
- **تحميل تدريجي** للبيانات
- **فهرسة قاعدة البيانات** المحسنة
- **ذاكرة تخزين مؤقت** للاستعلامات
- **ضغط الصور** المرفوعة

### التوافق:
- **متوافق مع جميع المتصفحات**
- **يدعم الأجهزة المحمولة**
- **واجهة عربية كاملة**
- **اتجاه RTL** صحيح

## 📊 التقارير والإحصائيات

### تقارير متاحة:
- تقرير الإجازات الشهرية
- تقرير رصيد الموظفين
- تقرير الطلبات المعلقة
- تقرير استخدام الإجازات

### إحصائيات فورية:
- عدد الطلبات المعلقة
- عدد الطلبات الموافق عليها
- عدد الطلبات المرفوضة
- إجمالي أيام الإجازات

## 🎯 خطط التطوير المستقبلية

### ميزات مخططة:
- **تقويم الإجازات** التفاعلي
- **إشعارات البريد الإلكتروني**
- **تطبيق الجوال** المصاحب
- **تكامل مع التقويم**
- **تقارير متقدمة** بالذكاء الاصطناعي

---

**🎉 نظام الإجازات جاهز للاستخدام بجميع الميزات المتقدمة!**

للدعم الفني أو الاستفسارات، يرجى مراجعة الوثائق أو التواصل مع فريق التطوير.
