@extends('layouts.app')

@section('title', 'إضافة راتب جديد')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4 animate-fade-in-down">
    <div>
        <h1 class="h3 mb-2 gradient-text fw-bold">
            <i class="bi bi-plus-circle me-2"></i>
            إضافة راتب جديد
        </h1>
        <p class="text-muted mb-0">إنشاء راتب شهري للموظف</p>
    </div>
    <a href="{{ route('payroll.index') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-right me-2"></i>
        العودة للقائمة
    </a>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="premium-card animate-fade-in-up">
            <div class="card-header bg-transparent border-0 py-4">
                <h5 class="mb-0 fw-bold">
                    <i class="bi bi-cash-stack me-2 text-primary"></i>
                    بيانات الراتب الشهري
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('payroll.store') }}" method="POST" id="payrollForm">
                    @csrf
                    
                    <div class="row">
                        <!-- اختيار الموظف -->
                        <div class="col-md-6 mb-4">
                            <label for="employee_id" class="form-label fw-semibold">
                                <i class="bi bi-person me-1"></i>
                                الموظف <span class="text-danger">*</span>
                            </label>
                            <select class="form-select select2 @error('employee_id') is-invalid @enderror" 
                                    id="employee_id" 
                                    name="employee_id" 
                                    required>
                                <option value="">اختر الموظف</option>
                                @foreach($employees as $employee)
                                    <option value="{{ $employee->id }}" 
                                            data-salary="{{ $employee->salary ?? 0 }}"
                                            {{ old('employee_id') == $employee->id ? 'selected' : '' }}>
                                        {{ $employee->full_name }} ({{ $employee->employee_id }})
                                    </option>
                                @endforeach
                            </select>
                            @error('employee_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- الشهر -->
                        <div class="col-md-3 mb-4">
                            <label for="month" class="form-label fw-semibold">
                                <i class="bi bi-calendar me-1"></i>
                                الشهر <span class="text-danger">*</span>
                            </label>
                            <select class="form-select @error('month') is-invalid @enderror" 
                                    id="month" 
                                    name="month" 
                                    required>
                                @for($i = 1; $i <= 12; $i++)
                                    <option value="{{ $i }}" {{ old('month', date('n')) == $i ? 'selected' : '' }}>
                                        {{ \Carbon\Carbon::create()->month($i)->translatedFormat('F') }}
                                    </option>
                                @endfor
                            </select>
                            @error('month')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- السنة -->
                        <div class="col-md-3 mb-4">
                            <label for="year" class="form-label fw-semibold">
                                <i class="bi bi-calendar-event me-1"></i>
                                السنة <span class="text-danger">*</span>
                            </label>
                            <select class="form-select @error('year') is-invalid @enderror" 
                                    id="year" 
                                    name="year" 
                                    required>
                                @for($year = date('Y'); $year >= 2020; $year--)
                                    <option value="{{ $year }}" {{ old('year', date('Y')) == $year ? 'selected' : '' }}>
                                        {{ $year }}
                                    </option>
                                @endfor
                            </select>
                            @error('year')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <!-- الراتب الأساسي -->
                        <div class="col-md-6 mb-4">
                            <label for="basic_salary" class="form-label fw-semibold">
                                <i class="bi bi-cash me-1 text-success"></i>
                                الراتب الأساسي <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="number" 
                                       class="form-control @error('basic_salary') is-invalid @enderror" 
                                       id="basic_salary" 
                                       name="basic_salary" 
                                       step="0.01"
                                       min="0"
                                       value="{{ old('basic_salary') }}" 
                                       required>
                                <span class="input-group-text">ر.س</span>
                            </div>
                            @error('basic_salary')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- البدلات -->
                        <div class="col-md-6 mb-4">
                            <label for="allowances" class="form-label fw-semibold">
                                <i class="bi bi-plus-circle me-1 text-info"></i>
                                البدلات
                            </label>
                            <div class="input-group">
                                <input type="number" 
                                       class="form-control @error('allowances') is-invalid @enderror" 
                                       id="allowances" 
                                       name="allowances" 
                                       step="0.01"
                                       min="0"
                                       value="{{ old('allowances', 0) }}">
                                <span class="input-group-text">ر.س</span>
                            </div>
                            @error('allowances')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <!-- ساعات العمل الإضافي -->
                        <div class="col-md-4 mb-4">
                            <label for="overtime_hours" class="form-label fw-semibold">
                                <i class="bi bi-clock me-1 text-warning"></i>
                                ساعات العمل الإضافي
                            </label>
                            <input type="number" 
                                   class="form-control @error('overtime_hours') is-invalid @enderror" 
                                   id="overtime_hours" 
                                   name="overtime_hours" 
                                   step="0.5"
                                   min="0"
                                   value="{{ old('overtime_hours', 0) }}">
                            @error('overtime_hours')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- معدل الساعة الإضافية -->
                        <div class="col-md-4 mb-4">
                            <label for="overtime_rate" class="form-label fw-semibold">
                                <i class="bi bi-currency-dollar me-1 text-warning"></i>
                                معدل الساعة الإضافية
                            </label>
                            <div class="input-group">
                                <input type="number" 
                                       class="form-control @error('overtime_rate') is-invalid @enderror" 
                                       id="overtime_rate" 
                                       name="overtime_rate" 
                                       step="0.01"
                                       min="0"
                                       value="{{ old('overtime_rate', 0) }}">
                                <span class="input-group-text">ر.س</span>
                            </div>
                            @error('overtime_rate')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- مبلغ العمل الإضافي -->
                        <div class="col-md-4 mb-4">
                            <label for="overtime_pay" class="form-label fw-semibold">
                                <i class="bi bi-calculator me-1 text-warning"></i>
                                مبلغ العمل الإضافي
                            </label>
                            <div class="input-group">
                                <input type="number" 
                                       class="form-control" 
                                       id="overtime_pay" 
                                       step="0.01"
                                       readonly>
                                <span class="input-group-text">ر.س</span>
                            </div>
                            <div class="form-text">يتم الحساب تلقائياً</div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- المكافآت -->
                        <div class="col-md-6 mb-4">
                            <label for="bonus" class="form-label fw-semibold">
                                <i class="bi bi-gift me-1 text-success"></i>
                                المكافآت
                            </label>
                            <div class="input-group">
                                <input type="number" 
                                       class="form-control @error('bonus') is-invalid @enderror" 
                                       id="bonus" 
                                       name="bonus" 
                                       step="0.01"
                                       min="0"
                                       value="{{ old('bonus', 0) }}">
                                <span class="input-group-text">ر.س</span>
                            </div>
                            @error('bonus')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- الخصومات -->
                        <div class="col-md-6 mb-4">
                            <label for="deductions" class="form-label fw-semibold">
                                <i class="bi bi-dash-circle me-1 text-danger"></i>
                                الخصومات
                            </label>
                            <div class="input-group">
                                <input type="number" 
                                       class="form-control @error('deductions') is-invalid @enderror" 
                                       id="deductions" 
                                       name="deductions" 
                                       step="0.01"
                                       min="0"
                                       value="{{ old('deductions', 0) }}">
                                <span class="input-group-text">ر.س</span>
                            </div>
                            @error('deductions')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- ملخص الراتب -->
                    <div class="row">
                        <div class="col-12 mb-4">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0 fw-bold">
                                        <i class="bi bi-calculator me-2"></i>
                                        ملخص الراتب
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-md-4 mb-3">
                                            <div class="border-end">
                                                <h5 class="text-primary mb-1" id="gross_salary_display">0.00</h5>
                                                <small class="text-muted">إجمالي الراتب</small>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="border-end">
                                                <h5 class="text-danger mb-1" id="total_deductions_display">0.00</h5>
                                                <small class="text-muted">إجمالي الخصومات</small>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <h4 class="text-success mb-1 fw-bold" id="net_salary_display">0.00</h4>
                                            <small class="text-muted">صافي الراتب</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="mb-4">
                        <label for="notes" class="form-label fw-semibold">
                            <i class="bi bi-chat-text me-1"></i>
                            ملاحظات
                        </label>
                        <textarea class="form-control @error('notes') is-invalid @enderror" 
                                  id="notes" 
                                  name="notes" 
                                  rows="3" 
                                  placeholder="أي ملاحظات إضافية...">{{ old('notes') }}</textarea>
                        @error('notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="bi bi-info-circle me-2"></i>
                            معلومات مهمة:
                        </h6>
                        <ul class="mb-0">
                            <li>سيتم حساب إجمالي الراتب وصافي الراتب تلقائياً</li>
                            <li>تأكد من صحة جميع البيانات قبل الحفظ</li>
                            <li>يمكن تعديل الراتب قبل الموافقة عليه</li>
                            <li>سيتم إرسال إشعار للموظف عند الموافقة</li>
                        </ul>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                            <i class="bi bi-x-circle me-2"></i>
                            إلغاء
                        </button>
                        <div>
                            <button type="reset" class="btn btn-outline-warning me-2">
                                <i class="bi bi-arrow-clockwise me-2"></i>
                                إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-premium">
                                <i class="bi bi-save me-2"></i>
                                حفظ الراتب
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap-5',
        placeholder: 'اختر الموظف',
        allowClear: true
    });

    // Auto-fill basic salary when employee is selected
    $('#employee_id').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const salary = selectedOption.data('salary') || 0;
        $('#basic_salary').val(salary);
        calculateSalary();
    });

    // Calculate salary automatically
    function calculateSalary() {
        const basicSalary = parseFloat($('#basic_salary').val()) || 0;
        const allowances = parseFloat($('#allowances').val()) || 0;
        const overtimeHours = parseFloat($('#overtime_hours').val()) || 0;
        const overtimeRate = parseFloat($('#overtime_rate').val()) || 0;
        const bonus = parseFloat($('#bonus').val()) || 0;
        const deductions = parseFloat($('#deductions').val()) || 0;

        // Calculate overtime pay
        const overtimePay = overtimeHours * overtimeRate;
        $('#overtime_pay').val(overtimePay.toFixed(2));

        // Calculate gross salary
        const grossSalary = basicSalary + allowances + overtimePay + bonus;

        // Calculate net salary
        const netSalary = grossSalary - deductions;

        // Update display
        $('#gross_salary_display').text(grossSalary.toFixed(2) + ' ر.س');
        $('#total_deductions_display').text(deductions.toFixed(2) + ' ر.س');
        $('#net_salary_display').text(netSalary.toFixed(2) + ' ر.س');

        // Update colors based on values
        if (netSalary > 0) {
            $('#net_salary_display').removeClass('text-danger').addClass('text-success');
        } else {
            $('#net_salary_display').removeClass('text-success').addClass('text-danger');
        }
    }

    // Auto-calculate when any field changes
    $('#basic_salary, #allowances, #overtime_hours, #overtime_rate, #bonus, #deductions').on('input', calculateSalary);

    // Form validation
    $('#payrollForm').on('submit', function(e) {
        const employeeId = $('#employee_id').val();
        const month = $('#month').val();
        const year = $('#year').val();
        const basicSalary = parseFloat($('#basic_salary').val()) || 0;

        if (!employeeId || !month || !year || basicSalary <= 0) {
            e.preventDefault();
            Swal.fire({
                title: 'خطأ في البيانات',
                text: 'يرجى ملء جميع الحقول المطلوبة والتأكد من أن الراتب الأساسي أكبر من صفر',
                icon: 'error',
                confirmButtonText: 'موافق'
            });
            return false;
        }

        // Show loading
        Swal.fire({
            title: 'جاري حفظ الراتب...',
            text: 'يرجى الانتظار',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    });

    // Initialize calculation on page load
    calculateSalary();
});
</script>
@endpush

@push('styles')
<style>
.premium-card {
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
    border-radius: 15px;
}

.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-premium {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 10px 25px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-premium:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.input-group-text {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-color: #dee2e6;
    font-weight: 600;
}

.card.bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
    border: 1px solid rgba(0,0,0,0.1);
}

.border-end {
    border-right: 2px solid #dee2e6 !important;
}

@media (max-width: 768px) {
    .border-end {
        border-right: none !important;
        border-bottom: 2px solid #dee2e6 !important;
        padding-bottom: 1rem;
        margin-bottom: 1rem;
    }
}
</style>
@endpush
