@extends('layouts.app')

@section('title', 'إدارة الحضور والانصراف')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="bi bi-clock me-2"></i>
        إدارة الحضور والانصراف
    </h1>
    <div class="btn-group">
        <a href="{{ route('attendance.create') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>
            تسجيل حضور
        </a>
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#quickCheckModal">
            <i class="bi bi-stopwatch me-2"></i>
            تسجيل سريع
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            الحضور اليوم
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ $attendances->where('date', today())->where('status', 'present')->count() }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            الغياب اليوم
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ $attendances->where('date', today())->where('status', 'absent')->count() }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-x fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            التأخير اليوم
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ $attendances->where('date', today())->where('status', 'late')->count() }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clock-history fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            متوسط ساعات العمل
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ number_format($attendances->where('date', today())->avg('total_hours'), 1) }} ساعة
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-hourglass-split fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="bi bi-table me-2"></i>
            سجلات الحضور والانصراف
        </h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered data-table" id="attendanceTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>الموظف</th>
                        <th>التاريخ</th>
                        <th>وقت الدخول</th>
                        <th>وقت الخروج</th>
                        <th>إجمالي الساعات</th>
                        <th>الحالة</th>
                        <th>ملاحظات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($attendances as $attendance)
                    <tr>
                        <td>
                            <div class="fw-bold">{{ $attendance->employee->full_name }}</div>
                            <small class="text-muted">{{ $attendance->employee->employee_id }}</small>
                        </td>
                        <td>{{ $attendance->date->format('Y/m/d') }}</td>
                        <td>
                            @if($attendance->check_in)
                                <span class="badge bg-success">{{ $attendance->check_in->format('H:i') }}</span>
                            @else
                                <span class="text-muted">-</span>
                            @endif
                        </td>
                        <td>
                            @if($attendance->check_out)
                                <span class="badge bg-info">{{ $attendance->check_out->format('H:i') }}</span>
                            @else
                                <span class="text-muted">-</span>
                            @endif
                        </td>
                        <td>
                            @if($attendance->total_hours > 0)
                                {{ number_format($attendance->total_hours, 1) }} ساعة
                            @else
                                <span class="text-muted">-</span>
                            @endif
                        </td>
                        <td>
                            @switch($attendance->status)
                                @case('present')
                                    <span class="badge bg-success">حاضر</span>
                                    @break
                                @case('absent')
                                    <span class="badge bg-danger">غائب</span>
                                    @break
                                @case('late')
                                    <span class="badge bg-warning">متأخر</span>
                                    @break
                                @case('half_day')
                                    <span class="badge bg-info">نصف يوم</span>
                                    @break
                                @case('holiday')
                                    <span class="badge bg-secondary">عطلة</span>
                                    @break
                                @default
                                    <span class="badge bg-secondary">{{ $attendance->status }}</span>
                            @endswitch
                        </td>
                        <td>
                            @if($attendance->notes)
                                <span title="{{ $attendance->notes }}">
                                    {{ Str::limit($attendance->notes, 30) }}
                                </span>
                            @else
                                <span class="text-muted">-</span>
                            @endif
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('attendance.show', $attendance) }}" 
                                   class="btn btn-sm btn-outline-info" 
                                   title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ route('attendance.edit', $attendance) }}" 
                                   class="btn btn-sm btn-outline-primary" 
                                   title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button type="button" 
                                        class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteAttendance({{ $attendance->id }})"
                                        title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="d-flex justify-content-center mt-3">
            {{ $attendances->links() }}
        </div>
    </div>
</div>

<!-- Quick Check Modal -->
<div class="modal fade" id="quickCheckModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تسجيل حضور سريع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="quickCheckForm">
                    @csrf
                    <div class="mb-3">
                        <label for="quick_employee_id" class="form-label">الموظف</label>
                        <select class="form-select select2" id="quick_employee_id" name="employee_id" required>
                            <option value="">اختر الموظف</option>
                            @foreach(\App\Models\Employee::where('employment_status', 'active')->get() as $employee)
                                <option value="{{ $employee->id }}">
                                    {{ $employee->full_name }} ({{ $employee->employee_id }})
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success" onclick="quickCheckIn()">
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            تسجيل دخول
                        </button>
                        <button type="button" class="btn btn-info" onclick="quickCheckOut()">
                            <i class="bi bi-box-arrow-right me-2"></i>
                            تسجيل خروج
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Delete Attendance Function
function deleteAttendance(attendanceId) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: "لن تتمكن من التراجع عن هذا الإجراء!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/attendance/${attendanceId}`;
            
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            
            const methodField = document.createElement('input');
            methodField.type = 'hidden';
            methodField.name = '_method';
            methodField.value = 'DELETE';
            
            form.appendChild(csrfToken);
            form.appendChild(methodField);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

// Quick Check In
function quickCheckIn() {
    const employeeId = document.getElementById('quick_employee_id').value;
    
    if (!employeeId) {
        Swal.fire('خطأ', 'يرجى اختيار الموظف', 'error');
        return;
    }
    
    fetch('/attendance/checkin', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            employee_id: employeeId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire('نجح', data.message, 'success').then(() => {
                location.reload();
            });
        } else {
            Swal.fire('خطأ', data.message, 'error');
        }
    })
    .catch(error => {
        Swal.fire('خطأ', 'حدث خطأ في الشبكة', 'error');
    });
}

// Quick Check Out
function quickCheckOut() {
    const employeeId = document.getElementById('quick_employee_id').value;
    
    if (!employeeId) {
        Swal.fire('خطأ', 'يرجى اختيار الموظف', 'error');
        return;
    }
    
    fetch('/attendance/checkout', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            employee_id: employeeId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire('نجح', data.message, 'success').then(() => {
                location.reload();
            });
        } else {
            Swal.fire('خطأ', data.message, 'error');
        }
    })
    .catch(error => {
        Swal.fire('خطأ', 'حدث خطأ في الشبكة', 'error');
    });
}

// Initialize DataTable
$(document).ready(function() {
    $('#attendanceTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        order: [[1, 'desc']] // Sort by date descending
    });
});
</script>
@endpush

@push('styles')
<style>
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    margin: 0 1px;
}

.badge {
    font-size: 0.75em;
}
</style>
@endpush
