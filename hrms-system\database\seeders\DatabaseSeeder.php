<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use App\Models\Department;
use App\Models\Employee;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create Roles
        $adminRole = Role::create([
            'name' => 'admin',
            'name_ar' => 'مدير النظام',
            'description' => 'Full system access',
            'permissions' => [
                'users.view', 'users.create', 'users.edit', 'users.delete',
                'employees.view', 'employees.create', 'employees.edit', 'employees.delete',
                'departments.view', 'departments.create', 'departments.edit', 'departments.delete',
                'attendance.view', 'attendance.create', 'attendance.edit', 'attendance.delete',
                'leaves.view', 'leaves.create', 'leaves.edit', 'leaves.delete', 'leaves.approve',
                'payroll.view', 'payroll.create', 'payroll.edit', 'payroll.delete',
                'reports.view', 'settings.view', 'settings.edit'
            ],
            'is_active' => true,
        ]);

        $hrRole = Role::create([
            'name' => 'hr',
            'name_ar' => 'موظف الموارد البشرية',
            'description' => 'HR management access',
            'permissions' => [
                'employees.view', 'employees.create', 'employees.edit',
                'departments.view', 'attendance.view', 'attendance.create',
                'leaves.view', 'leaves.approve', 'payroll.view', 'reports.view'
            ],
            'is_active' => true,
        ]);

        $employeeRole = Role::create([
            'name' => 'employee',
            'name_ar' => 'موظف',
            'description' => 'Basic employee access',
            'permissions' => [
                'attendance.view', 'attendance.create', 'leaves.view', 'leaves.create'
            ],
            'is_active' => true,
        ]);

        // Create Admin User
        $adminUser = User::create([
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'role_id' => $adminRole->id,
            'is_active' => true,
        ]);

        // Create HR User
        $hrUser = User::create([
            'name' => 'موظف الموارد البشرية',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'role_id' => $hrRole->id,
            'is_active' => true,
        ]);

        // Create Departments
        $itDepartment = Department::create([
            'name' => 'Information Technology',
            'name_ar' => 'تقنية المعلومات',
            'description' => 'IT Department',
            'description_ar' => 'قسم تقنية المعلومات',
            'manager_name' => 'أحمد محمد',
            'location' => 'الطابق الثالث',
            'is_active' => true,
        ]);

        $hrDepartment = Department::create([
            'name' => 'Human Resources',
            'name_ar' => 'الموارد البشرية',
            'description' => 'HR Department',
            'description_ar' => 'قسم الموارد البشرية',
            'manager_name' => 'فاطمة أحمد',
            'location' => 'الطابق الثاني',
            'is_active' => true,
        ]);

        $financeDepartment = Department::create([
            'name' => 'Finance',
            'name_ar' => 'المالية',
            'description' => 'Finance Department',
            'description_ar' => 'قسم المالية',
            'manager_name' => 'محمد علي',
            'location' => 'الطابق الأول',
            'is_active' => true,
        ]);

        // Create Sample Employees
        Employee::create([
            'employee_id' => 'EMP001',
            'user_id' => $adminUser->id,
            'department_id' => $itDepartment->id,
            'first_name' => 'أحمد',
            'last_name' => 'محمد',
            'first_name_ar' => 'أحمد',
            'last_name_ar' => 'محمد',
            'email' => '<EMAIL>',
            'phone' => '0501234567',
            'national_id' => '1234567890',
            'birth_date' => '1990-01-01',
            'gender' => 'male',
            'marital_status' => 'married',
            'address' => 'الرياض، المملكة العربية السعودية',
            'position' => 'System Administrator',
            'position_ar' => 'مدير النظام',
            'hire_date' => '2023-01-01',
            'salary' => 15000.00,
            'contract_type' => 'full_time',
            'employment_status' => 'active',
            'annual_leave_balance' => 21,
        ]);

        Employee::create([
            'employee_id' => 'EMP002',
            'user_id' => $hrUser->id,
            'department_id' => $hrDepartment->id,
            'first_name' => 'فاطمة',
            'last_name' => 'أحمد',
            'first_name_ar' => 'فاطمة',
            'last_name_ar' => 'أحمد',
            'email' => '<EMAIL>',
            'phone' => '0507654321',
            'national_id' => '0987654321',
            'birth_date' => '1992-05-15',
            'gender' => 'female',
            'marital_status' => 'single',
            'address' => 'جدة، المملكة العربية السعودية',
            'position' => 'HR Specialist',
            'position_ar' => 'أخصائي موارد بشرية',
            'hire_date' => '2023-02-01',
            'salary' => 12000.00,
            'contract_type' => 'full_time',
            'employment_status' => 'active',
            'annual_leave_balance' => 21,
        ]);
    }
}
