<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Attendance;
use App\Models\Employee;
use Carbon\Carbon;

class AttendanceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $attendances = Attendance::with('employee')
            ->orderBy('date', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(50);

        return view('attendance.index', compact('attendances'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $employees = Employee::where('employment_status', 'active')
            ->orderBy('first_name')
            ->get();

        return view('attendance.create', compact('employees'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'date' => 'required|date',
            'check_in' => 'nullable|date_format:H:i',
            'check_out' => 'nullable|date_format:H:i|after:check_in',
            'status' => 'required|in:present,absent,late,half_day,holiday',
            'notes' => 'nullable|string|max:500',
        ]);

        // Check if attendance already exists for this employee and date
        $existingAttendance = Attendance::where('employee_id', $request->employee_id)
            ->where('date', $request->date)
            ->first();

        if ($existingAttendance) {
            return back()->withErrors(['date' => 'تم تسجيل الحضور لهذا الموظف في هذا التاريخ مسبقاً']);
        }

        // Calculate total hours
        $totalHours = 0;
        if ($request->check_in && $request->check_out) {
            $checkIn = Carbon::createFromFormat('H:i', $request->check_in);
            $checkOut = Carbon::createFromFormat('H:i', $request->check_out);
            $totalHours = $checkOut->diffInHours($checkIn);
        }

        Attendance::create([
            'employee_id' => $request->employee_id,
            'date' => $request->date,
            'check_in' => $request->check_in,
            'check_out' => $request->check_out,
            'total_hours' => $totalHours,
            'status' => $request->status,
            'notes' => $request->notes,
            'ip_address' => $request->ip(),
        ]);

        return redirect()->route('attendance.index')
            ->with('success', 'تم تسجيل الحضور بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Attendance $attendance)
    {
        $attendance->load('employee.department');

        // Get monthly statistics for this employee
        $currentMonth = Carbon::now()->format('Y-m');
        $monthlyStats = Attendance::where('employee_id', $attendance->employee_id)
            ->whereYear('date', Carbon::now()->year)
            ->whereMonth('date', Carbon::now()->month)
            ->selectRaw('
                COUNT(CASE WHEN status = "present" THEN 1 END) as present,
                COUNT(CASE WHEN status = "absent" THEN 1 END) as absent,
                COUNT(CASE WHEN status = "late" THEN 1 END) as late,
                SUM(total_hours) as total_hours
            ')
            ->first();

        return view('attendance.show', compact('attendance', 'monthlyStats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Attendance $attendance)
    {
        $employees = Employee::where('employment_status', 'active')
            ->orderBy('first_name')
            ->get();

        return view('attendance.edit', compact('attendance', 'employees'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Attendance $attendance)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'date' => 'required|date',
            'check_in' => 'nullable|date_format:H:i',
            'check_out' => 'nullable|date_format:H:i|after:check_in',
            'status' => 'required|in:present,absent,late,half_day,holiday',
            'notes' => 'nullable|string|max:500',
        ]);

        // Calculate total hours
        $totalHours = 0;
        if ($request->check_in && $request->check_out) {
            $checkIn = Carbon::createFromFormat('H:i', $request->check_in);
            $checkOut = Carbon::createFromFormat('H:i', $request->check_out);
            $totalHours = $checkOut->diffInHours($checkIn);
        }

        $attendance->update([
            'employee_id' => $request->employee_id,
            'date' => $request->date,
            'check_in' => $request->check_in,
            'check_out' => $request->check_out,
            'total_hours' => $totalHours,
            'status' => $request->status,
            'notes' => $request->notes,
        ]);

        return redirect()->route('attendance.index')
            ->with('success', 'تم تحديث سجل الحضور بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Attendance $attendance)
    {
        $attendance->delete();

        return redirect()->route('attendance.index')
            ->with('success', 'تم حذف سجل الحضور بنجاح');
    }

    /**
     * Quick check-in for employees.
     */
    public function checkIn(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
        ]);

        $today = Carbon::today();

        // Check if already checked in today
        $existingAttendance = Attendance::where('employee_id', $request->employee_id)
            ->where('date', $today)
            ->first();

        if ($existingAttendance) {
            return response()->json([
                'success' => false,
                'message' => 'تم تسجيل الحضور لهذا اليوم مسبقاً'
            ]);
        }

        $attendance = Attendance::create([
            'employee_id' => $request->employee_id,
            'date' => $today,
            'check_in' => Carbon::now()->format('H:i'),
            'status' => 'present',
            'ip_address' => $request->ip(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تسجيل الحضور بنجاح',
            'data' => $attendance
        ]);
    }

    /**
     * Quick check-out for employees.
     */
    public function checkOut(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
        ]);

        $today = Carbon::today();

        $attendance = Attendance::where('employee_id', $request->employee_id)
            ->where('date', $today)
            ->first();

        if (!$attendance) {
            return response()->json([
                'success' => false,
                'message' => 'لم يتم العثور على سجل حضور لهذا اليوم'
            ]);
        }

        if ($attendance->check_out) {
            return response()->json([
                'success' => false,
                'message' => 'تم تسجيل الانصراف مسبقاً'
            ]);
        }

        $checkOut = Carbon::now();
        $checkIn = Carbon::createFromFormat('H:i', $attendance->check_in);
        $totalHours = $checkOut->diffInHours($checkIn);

        $attendance->update([
            'check_out' => $checkOut->format('H:i'),
            'total_hours' => $totalHours,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تسجيل الانصراف بنجاح',
            'data' => $attendance
        ]);
    }
}
