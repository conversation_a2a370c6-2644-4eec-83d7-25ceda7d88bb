@extends('layouts.app')

@section('title', 'تفاصيل الحضور')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4 animate-fade-in-down">
    <div>
        <h1 class="h3 mb-2 gradient-text fw-bold">
            <i class="bi bi-eye me-2"></i>
            تفاصيل سجل الحضور
        </h1>
        <p class="text-muted mb-0">عرض تفاصيل حضور وانصراف الموظف</p>
    </div>
    <div class="btn-group">
        <a href="{{ route('attendance.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للقائمة
        </a>
        <a href="{{ route('attendance.edit', $attendance) }}" class="btn btn-outline-primary">
            <i class="bi bi-pencil me-2"></i>
            تعديل
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="premium-card animate-fade-in-up">
            <div class="card-header bg-transparent border-0 py-4">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-bold">
                        <i class="bi bi-info-circle me-2 text-primary"></i>
                        معلومات الحضور
                    </h5>
                    <span class="badge badge-premium bg-{{ $attendance->status === 'present' ? 'success' : ($attendance->status === 'absent' ? 'danger' : 'warning') }}">
                        @switch($attendance->status)
                            @case('present')
                                حاضر
                                @break
                            @case('absent')
                                غائب
                                @break
                            @case('late')
                                متأخر
                                @break
                            @case('half_day')
                                نصف يوم
                                @break
                            @case('holiday')
                                عطلة
                                @break
                            @default
                                {{ $attendance->status }}
                        @endswitch
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- معلومات الموظف -->
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-person-fill me-2 text-primary"></i>
                                الموظف
                            </label>
                            <div class="info-value">
                                <div class="fw-bold">{{ $attendance->employee->full_name }}</div>
                                <small class="text-muted">{{ $attendance->employee->employee_id }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- القسم -->
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-building me-2 text-info"></i>
                                القسم
                            </label>
                            <div class="info-value">
                                {{ $attendance->employee->department->name_ar ?? $attendance->employee->department->name ?? 'غير محدد' }}
                            </div>
                        </div>
                    </div>

                    <!-- التاريخ -->
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-calendar3 me-2 text-success"></i>
                                التاريخ
                            </label>
                            <div class="info-value">
                                {{ $attendance->date->format('Y/m/d') }}
                                <small class="text-muted d-block">{{ $attendance->date->format('l') }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- وقت الدخول -->
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-box-arrow-in-right me-2 text-success"></i>
                                وقت الدخول
                            </label>
                            <div class="info-value">
                                @if($attendance->check_in)
                                    <span class="badge bg-success fs-6">{{ $attendance->check_in->format('H:i') }}</span>
                                @else
                                    <span class="text-muted">لم يتم التسجيل</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- وقت الخروج -->
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-box-arrow-right me-2 text-info"></i>
                                وقت الخروج
                            </label>
                            <div class="info-value">
                                @if($attendance->check_out)
                                    <span class="badge bg-info fs-6">{{ $attendance->check_out->format('H:i') }}</span>
                                @else
                                    <span class="text-muted">لم يتم التسجيل</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- إجمالي الساعات -->
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-hourglass-split me-2 text-warning"></i>
                                إجمالي الساعات
                            </label>
                            <div class="info-value">
                                @if($attendance->total_hours > 0)
                                    <span class="badge bg-warning text-dark fs-6">{{ number_format($attendance->total_hours, 1) }} ساعة</span>
                                @else
                                    <span class="text-muted">غير محسوب</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    @if($attendance->notes)
                    <div class="col-12 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-chat-text me-2 text-secondary"></i>
                                الملاحظات
                            </label>
                            <div class="info-value">
                                <div class="alert alert-light border">
                                    {{ $attendance->notes }}
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- معلومات إضافية -->
                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-clock-history me-2 text-muted"></i>
                                تاريخ الإنشاء
                            </label>
                            <div class="info-value">
                                <small class="text-muted">
                                    {{ $attendance->created_at->format('Y/m/d H:i') }}
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="info-group">
                            <label class="info-label">
                                <i class="bi bi-pencil-square me-2 text-muted"></i>
                                آخر تحديث
                            </label>
                            <div class="info-value">
                                <small class="text-muted">
                                    {{ $attendance->updated_at->format('Y/m/d H:i') }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="row mt-4 pt-4 border-top">
                    <div class="col-12">
                        <h6 class="fw-bold mb-3">
                            <i class="bi bi-graph-up me-2"></i>
                            إحصائيات الموظف هذا الشهر
                        </h6>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="stat-item">
                            <div class="stat-number text-success">{{ $monthlyStats['present'] ?? 0 }}</div>
                            <div class="stat-label">أيام حضور</div>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="stat-item">
                            <div class="stat-number text-danger">{{ $monthlyStats['absent'] ?? 0 }}</div>
                            <div class="stat-label">أيام غياب</div>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="stat-item">
                            <div class="stat-number text-warning">{{ $monthlyStats['late'] ?? 0 }}</div>
                            <div class="stat-label">أيام تأخير</div>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="stat-item">
                            <div class="stat-number text-info">{{ number_format($monthlyStats['total_hours'] ?? 0, 1) }}</div>
                            <div class="stat-label">إجمالي الساعات</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.premium-card {
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
    border-radius: 15px;
}

.info-group {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    height: 100%;
}

.info-label {
    font-size: 14px;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 8px;
    display: block;
}

.info-value {
    font-size: 16px;
    color: #495057;
    font-weight: 500;
}

.stat-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 15px;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.badge-premium {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 12px;
}
</style>
@endpush
