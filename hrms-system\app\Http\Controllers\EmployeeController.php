<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Employee;
use App\Models\Department;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class EmployeeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $employees = Employee::with(['department', 'user'])
            ->orderBy('created_at', 'desc')
            ->get();

        return view('employees.index', compact('employees'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $departments = Department::where('is_active', true)->get();
        $roles = Role::where('is_active', true)->get();

        return view('employees.create', compact('departments', 'roles'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:employees,email',
            'phone' => 'nullable|string|max:20',
            'national_id' => 'required|string|unique:employees,national_id',
            'department_id' => 'required|exists:departments,id',
            'position' => 'required|string|max:255',
            'hire_date' => 'required|date',
            'salary' => 'required|numeric|min:0',
            'birth_date' => 'nullable|date',
            'gender' => 'nullable|in:male,female',
            'marital_status' => 'nullable|in:single,married,divorced,widowed',
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        // Generate employee ID
        $lastEmployee = Employee::orderBy('id', 'desc')->first();
        $employeeId = 'EMP' . str_pad(($lastEmployee ? $lastEmployee->id + 1 : 1), 3, '0', STR_PAD_LEFT);

        // Handle profile picture upload
        $profilePicture = null;
        if ($request->hasFile('profile_picture')) {
            $profilePicture = $request->file('profile_picture')->store('employees', 'public');
        }

        // Create user account if requested
        $user = null;
        if ($request->has('create_user_account')) {
            $user = User::create([
                'name' => $request->first_name . ' ' . $request->last_name,
                'email' => $request->email,
                'password' => Hash::make($request->password ?? '123456'),
                'role_id' => $request->role_id,
                'is_active' => true,
            ]);
        }

        // Create employee
        $employee = Employee::create([
            'employee_id' => $employeeId,
            'user_id' => $user ? $user->id : null,
            'department_id' => $request->department_id,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'first_name_ar' => $request->first_name_ar,
            'last_name_ar' => $request->last_name_ar,
            'email' => $request->email,
            'phone' => $request->phone,
            'national_id' => $request->national_id,
            'birth_date' => $request->birth_date,
            'gender' => $request->gender,
            'marital_status' => $request->marital_status,
            'address' => $request->address,
            'position' => $request->position,
            'position_ar' => $request->position_ar,
            'hire_date' => $request->hire_date,
            'salary' => $request->salary,
            'contract_type' => $request->contract_type ?? 'full_time',
            'employment_status' => 'active',
            'emergency_contact_name' => $request->emergency_contact_name,
            'emergency_contact_phone' => $request->emergency_contact_phone,
            'bank_account' => $request->bank_account,
            'bank_name' => $request->bank_name,
            'profile_picture' => $profilePicture,
            'annual_leave_balance' => 21,
        ]);

        return redirect()->route('employees.index')
            ->with('success', 'تم إضافة الموظف بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Employee $employee)
    {
        $employee->load(['department', 'user', 'attendances', 'leaves', 'payrolls']);

        return view('employees.show', compact('employee'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Employee $employee)
    {
        $departments = Department::where('is_active', true)->get();
        $roles = Role::where('is_active', true)->get();

        return view('employees.edit', compact('employee', 'departments', 'roles'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Employee $employee)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:employees,email,' . $employee->id,
            'phone' => 'nullable|string|max:20',
            'national_id' => 'required|string|unique:employees,national_id,' . $employee->id,
            'department_id' => 'required|exists:departments,id',
            'position' => 'required|string|max:255',
            'hire_date' => 'required|date',
            'salary' => 'required|numeric|min:0',
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        // Handle profile picture upload
        if ($request->hasFile('profile_picture')) {
            // Delete old picture
            if ($employee->profile_picture) {
                Storage::disk('public')->delete($employee->profile_picture);
            }
            $profilePicture = $request->file('profile_picture')->store('employees', 'public');
            $employee->profile_picture = $profilePicture;
        }

        // Update employee
        $employee->update($request->except(['profile_picture']));

        return redirect()->route('employees.index')
            ->with('success', 'تم تحديث بيانات الموظف بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Employee $employee)
    {
        // Delete profile picture
        if ($employee->profile_picture) {
            Storage::disk('public')->delete($employee->profile_picture);
        }

        // Delete user account if exists
        if ($employee->user) {
            $employee->user->delete();
        }

        $employee->delete();

        return redirect()->route('employees.index')
            ->with('success', 'تم حذف الموظف بنجاح');
    }

    /**
     * Show employee profile.
     */
    public function profile(Employee $employee)
    {
        $employee->load(['department', 'user', 'attendances' => function($query) {
            $query->orderBy('date', 'desc')->limit(10);
        }, 'leaves' => function($query) {
            $query->orderBy('created_at', 'desc')->limit(5);
        }]);

        return view('employees.profile', compact('employee'));
    }
}
