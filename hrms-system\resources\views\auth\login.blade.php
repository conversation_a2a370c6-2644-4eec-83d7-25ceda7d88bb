<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>تسجيل الدخول - نظام إدارة الموارد البشرية</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{{ asset('assets/images/icon.svg') }}">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <!-- Premium Styles -->
    <link href="{{ asset('assets/css/premium-style.css') }}" rel="stylesheet">
    
    <style>
        /* Premium Login Styles */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1.5" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.8" fill="rgba(255,255,255,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: grain 20s linear infinite;
            pointer-events: none;
        }

        @keyframes grain {
            0%, 100% { transform: translate(0, 0); }
            10% { transform: translate(-5%, -10%); }
            20% { transform: translate(-15%, 5%); }
            30% { transform: translate(7%, -25%); }
            40% { transform: translate(-5%, 25%); }
            50% { transform: translate(-15%, 10%); }
            60% { transform: translate(15%, 0%); }
            70% { transform: translate(0%, 15%); }
            80% { transform: translate(3%, -10%); }
            90% { transform: translate(-10%, 5%); }
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            max-width: 1000px;
            width: 100%;
            position: relative;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
        }

        .login-left {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            padding: 80px 50px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .login-right {
            padding: 80px 50px;
            position: relative;
        }

        .logo-container {
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .logo-icon {
            width: 120px;
            height: 120px;
            margin-bottom: 20px;
            filter: drop-shadow(0 10px 20px rgba(0,0,0,0.2));
            animation: float 3s ease-in-out infinite;
        }

        .form-control-premium {
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 16px 20px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-control-premium:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
        }

        .input-group-premium {
            position: relative;
            margin-bottom: 24px;
        }

        .input-group-premium .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
            z-index: 3;
            transition: all 0.3s ease;
        }

        .input-group-premium .form-control-premium {
            padding-left: 50px;
        }

        .btn-login-premium {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 16px 32px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-login-premium::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login-premium:hover::before {
            left: 100%;
        }

        .btn-login-premium:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-login-premium:active {
            transform: translateY(-1px);
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 12px 0;
            position: relative;
            z-index: 2;
        }

        .feature-item i {
            font-size: 24px;
            margin-left: 20px;
            color: rgba(255, 255, 255, 0.9);
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
            pointer-events: none;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 100px;
            height: 100px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 15%;
            left: 20%;
            animation-delay: 4s;
        }

        .shape:nth-child(4) {
            width: 60px;
            height: 60px;
            top: 30%;
            right: 30%;
            animation-delay: 1s;
        }

        .shape:nth-child(5) {
            width: 120px;
            height: 120px;
            bottom: 40%;
            right: 5%;
            animation-delay: 3s;
        }

        @media (max-width: 768px) {
            .login-left,
            .login-right {
                padding: 40px 30px;
            }

            .logo-icon {
                width: 80px;
                height: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="login-container">
        <div class="login-card animate-fade-in-up">
            <div class="row g-0">
                <!-- Left Side -->
                <div class="col-lg-6 login-left">
                    <div class="logo-container animate-float">
                        <img src="{{ asset('assets/images/icon.svg') }}" alt="HRMS Logo" class="logo-icon">
                    </div>
                    <h1 class="mb-3 fw-bold" style="font-size: 2.2rem;">نظام إدارة الموارد البشرية</h1>
                    <p class="mb-5 fs-5 opacity-90">نظام متكامل وذكي لإدارة جميع شؤون الموظفين بكفاءة عالية</p>

                    <div class="features">
                        <div class="feature-item animate-slide-in-left" style="animation-delay: 0.2s;">
                            <i class="bi bi-people-fill"></i>
                            <span>إدارة شاملة للموظفين والأقسام</span>
                        </div>
                        <div class="feature-item animate-slide-in-left" style="animation-delay: 0.4s;">
                            <i class="bi bi-clock-fill"></i>
                            <span>تتبع دقيق للحضور والانصراف</span>
                        </div>
                        <div class="feature-item animate-slide-in-left" style="animation-delay: 0.6s;">
                            <i class="bi bi-calendar-check-fill"></i>
                            <span>نظام متطور للإجازات والمرتبات</span>
                        </div>
                        <div class="feature-item animate-slide-in-left" style="animation-delay: 0.8s;">
                            <i class="bi bi-graph-up-arrow"></i>
                            <span>تقارير تحليلية شاملة ومفصلة</span>
                        </div>
                        <div class="feature-item animate-slide-in-left" style="animation-delay: 1s;">
                            <i class="bi bi-shield-check-fill"></i>
                            <span>أمان متقدم وحماية البيانات</span>
                        </div>
                    </div>
                </div>
                
                <!-- Right Side -->
                <div class="col-lg-6 login-right">
                    <div class="text-center mb-5 animate-fade-in-down">
                        <img src="{{ asset('assets/images/logo.svg') }}" alt="HRMS Logo" style="height: 60px; margin-bottom: 20px;">
                        <h2 class="fw-bold gradient-text mb-2">مرحباً بك مرة أخرى</h2>
                        <p class="text-muted fs-6">قم بتسجيل الدخول للوصول إلى لوحة التحكم الخاصة بك</p>
                    </div>

                    @if ($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show animate-fade-in-up" role="alert" style="border-radius: 12px; border: none; background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);">
                            <i class="bi bi-exclamation-triangle-fill me-2 text-danger"></i>
                            @foreach ($errors->all() as $error)
                                {{ $error }}
                            @endforeach
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <form method="POST" action="{{ route('login') }}" class="animate-fade-in-up" style="animation-delay: 0.3s;">
                        @csrf

                        <div class="input-group-premium">
                            <i class="bi bi-envelope-fill input-icon"></i>
                            <input type="email"
                                   class="form-control-premium @error('email') is-invalid @enderror"
                                   id="email"
                                   name="email"
                                   value="{{ old('email') }}"
                                   required
                                   autocomplete="email"
                                   autofocus
                                   placeholder="البريد الإلكتروني">
                        </div>

                        <div class="input-group-premium">
                            <i class="bi bi-lock-fill input-icon"></i>
                            <input type="password"
                                   class="form-control-premium @error('password') is-invalid @enderror"
                                   id="password"
                                   name="password"
                                   required
                                   autocomplete="current-password"
                                   placeholder="كلمة المرور">
                            <button class="btn btn-link position-absolute" type="button" id="togglePassword"
                                    style="right: 16px; top: 50%; transform: translateY(-50%); z-index: 3; border: none; background: none; color: #667eea;">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="remember" id="remember"
                                       style="border-color: #667eea;">
                                <label class="form-check-label text-muted" for="remember">
                                    تذكرني
                                </label>
                            </div>
                            <a href="#" class="text-decoration-none" style="color: #667eea; font-size: 14px;">
                                نسيت كلمة المرور؟
                            </a>
                        </div>

                        <button type="submit" class="btn-login-premium mb-4">
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            تسجيل الدخول
                        </button>

                        <!-- Demo Accounts -->
                        <div class="demo-accounts p-3 mb-4" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border-radius: 12px; border: 1px solid #e2e8f0;">
                            <h6 class="text-center mb-3 fw-bold" style="color: #475569;">حسابات تجريبية</h6>
                            <div class="row text-center">
                                <div class="col-6">
                                    <small class="d-block fw-semibold text-primary">مدير النظام</small>
                                    <small class="text-muted"><EMAIL></small>
                                    <small class="d-block text-muted">123456</small>
                                </div>
                                <div class="col-6">
                                    <small class="d-block fw-semibold text-success">موظف HR</small>
                                    <small class="text-muted"><EMAIL></small>
                                    <small class="d-block text-muted">123456</small>
                                </div>
                            </div>
                        </div>
                    </form>

                    <div class="text-center mt-4 animate-fade-in-up" style="animation-delay: 0.6s;">
                        <small class="text-muted">
                            © 2025 نظام إدارة الموارد البشرية. جميع الحقوق محفوظة.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Toggle Password Visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            }
        });
        
        // Form Validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                e.preventDefault();
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'يرجى ملء جميع الحقول المطلوبة',
                    confirmButtonText: 'موافق'
                });
            }
        });
    </script>
</body>
</html>
