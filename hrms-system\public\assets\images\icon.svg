<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="30" cy="30" r="28" fill="url(#iconGradient)" stroke="url(#iconBorder)" stroke-width="2"/>
  
  <!-- HR Icon -->
  <g transform="translate(20, 20)">
    <!-- People Icons -->
    <circle cx="7" cy="6" r="3" fill="white" opacity="0.95"/>
    <circle cx="13" cy="6" r="3" fill="white" opacity="0.95"/>
    
    <!-- Bodies -->
    <path d="M3 12 C3 10.5, 4.5 9, 7 9 C9.5 9, 11 10.5, 11 12 L11 18 L3 18 Z" fill="white" opacity="0.95"/>
    <path d="M9 12 C9 10.5, 10.5 9, 13 9 C15.5 9, 17 10.5, 17 12 L17 18 L9 18 Z" fill="white" opacity="0.95"/>
    
    <!-- Connection Line -->
    <line x1="7" y1="14" x2="13" y2="14" stroke="white" stroke-width="1.5" opacity="0.9"/>
    <circle cx="10" cy="14" r="1" fill="white" opacity="0.9"/>
  </g>
  
  <!-- Decorative Dots -->
  <circle cx="45" cy="15" r="2" fill="white" opacity="0.3"/>
  <circle cx="50" cy="25" r="1.5" fill="white" opacity="0.2"/>
  <circle cx="47" cy="35" r="1.8" fill="white" opacity="0.25"/>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="iconBorder" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
    </linearGradient>
  </defs>
</svg>
