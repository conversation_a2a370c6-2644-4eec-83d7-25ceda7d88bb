<svg width="200" height="60" viewBox="0 0 200 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="30" cy="30" r="25" fill="url(#gradient1)" stroke="url(#gradient2)" stroke-width="2"/>
  
  <!-- HR Icon -->
  <g transform="translate(15, 15)">
    <!-- People Icons -->
    <circle cx="10" cy="8" r="4" fill="white" opacity="0.9"/>
    <circle cx="20" cy="8" r="4" fill="white" opacity="0.9"/>
    
    <!-- Bodies -->
    <path d="M5 16 C5 14, 7 12, 10 12 C13 12, 15 14, 15 16 L15 25 L5 25 Z" fill="white" opacity="0.9"/>
    <path d="M15 16 C15 14, 17 12, 20 12 C23 12, 25 14, 25 16 L25 25 L15 25 Z" fill="white" opacity="0.9"/>
    
    <!-- Connection Line -->
    <line x1="10" y1="20" x2="20" y2="20" stroke="white" stroke-width="2" opacity="0.8"/>
  </g>
  
  <!-- Company Name -->
  <text x="65" y="25" font-family="'Segoe UI', Arial, sans-serif" font-size="18" font-weight="700" fill="url(#textGradient)">
    نظام الموارد البشرية
  </text>
  
  <!-- Subtitle -->
  <text x="65" y="42" font-family="'Segoe UI', Arial, sans-serif" font-size="11" font-weight="400" fill="#64748b">
    Human Resources Management System
  </text>
  
  <!-- Decorative Elements -->
  <circle cx="180" cy="15" r="3" fill="url(#accent1)" opacity="0.6"/>
  <circle cx="190" cy="25" r="2" fill="url(#accent2)" opacity="0.4"/>
  <circle cx="185" cy="35" r="2.5" fill="url(#accent1)" opacity="0.5"/>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="accent1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="accent2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
