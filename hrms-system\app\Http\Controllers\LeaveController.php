<?php

namespace App\Http\Controllers;

use App\Models\Leave;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class LeaveController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Leave::with(['employee.department']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }

        $leaves = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('leaves.index', compact('leaves'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $employees = Employee::where('employment_status', 'active')
                           ->orderBy('first_name')
                           ->get();

        return view('leaves.create', compact('employees'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'type' => 'required|in:annual,sick,emergency,maternity,paternity,unpaid',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after_or_equal:start_date',
            'reason' => 'required|string|max:1000',
            'is_half_day' => 'boolean',
            'half_day_period' => 'nullable|in:morning,afternoon',
            'attachment' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120'
        ]);

        // Calculate days requested
        $startDate = Carbon::parse($validated['start_date']);
        $endDate = Carbon::parse($validated['end_date']);
        $daysRequested = $endDate->diffInDays($startDate) + 1;

        if ($request->boolean('is_half_day') && $daysRequested == 1) {
            $daysRequested = 0.5;
        }

        $validated['days_requested'] = $daysRequested;
        $validated['status'] = 'pending';
        $validated['requested_by'] = Auth::id();

        // Handle file upload
        if ($request->hasFile('attachment')) {
            $validated['attachment'] = $request->file('attachment')->store('leave_attachments', 'public');
        }

        Leave::create($validated);

        return redirect()->route('leaves.index')
                        ->with('success', 'تم تقديم طلب الإجازة بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Leave $leave)
    {
        $leave->load(['employee.department', 'approvedBy']);

        // Get employee statistics
        $employeeStats = Leave::where('employee_id', $leave->employee_id)
            ->selectRaw('
                COUNT(CASE WHEN status = "approved" THEN 1 END) as approved,
                COUNT(CASE WHEN status = "pending" THEN 1 END) as pending,
                COUNT(CASE WHEN status = "rejected" THEN 1 END) as rejected,
                SUM(CASE WHEN status = "approved" THEN days_requested ELSE 0 END) as total_days
            ')
            ->first();

        // Get leave balance (mock data - should be calculated based on company policy)
        $leaveBalance = [
            'annual' => 21, // Should be calculated from employee's annual leave entitlement
            'sick' => 30    // Should be calculated from employee's sick leave entitlement
        ];

        return view('leaves.show', compact('leave', 'employeeStats', 'leaveBalance'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Leave $leave)
    {
        // Only allow editing if leave is pending
        if ($leave->status !== 'pending') {
            return redirect()->route('leaves.show', $leave)
                           ->with('error', 'لا يمكن تعديل الطلب بعد اتخاذ قرار بشأنه');
        }

        $employees = Employee::where('employment_status', 'active')
                           ->orderBy('first_name')
                           ->get();

        return view('leaves.edit', compact('leave', 'employees'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Leave $leave)
    {
        // Only allow updating if leave is pending
        if ($leave->status !== 'pending') {
            return redirect()->route('leaves.show', $leave)
                           ->with('error', 'لا يمكن تعديل الطلب بعد اتخاذ قرار بشأنه');
        }

        $validated = $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'type' => 'required|in:annual,sick,emergency,maternity,paternity,unpaid',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'reason' => 'required|string|max:1000',
            'is_half_day' => 'boolean',
            'half_day_period' => 'nullable|in:morning,afternoon',
            'attachment' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120'
        ]);

        // Calculate days requested
        $startDate = Carbon::parse($validated['start_date']);
        $endDate = Carbon::parse($validated['end_date']);
        $daysRequested = $endDate->diffInDays($startDate) + 1;

        if ($request->boolean('is_half_day') && $daysRequested == 1) {
            $daysRequested = 0.5;
        }

        $validated['days_requested'] = $daysRequested;

        // Handle file upload
        if ($request->hasFile('attachment')) {
            // Delete old attachment if exists
            if ($leave->attachment) {
                Storage::disk('public')->delete($leave->attachment);
            }
            $validated['attachment'] = $request->file('attachment')->store('leave_attachments', 'public');
        }

        $leave->update($validated);

        return redirect()->route('leaves.show', $leave)
                        ->with('success', 'تم تحديث طلب الإجازة بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Leave $leave)
    {
        // Delete attachment if exists
        if ($leave->attachment) {
            Storage::disk('public')->delete($leave->attachment);
        }

        $leave->delete();

        return redirect()->route('leaves.index')
                        ->with('success', 'تم حذف طلب الإجازة بنجاح');
    }

    /**
     * Approve a leave request
     */
    public function approve(Request $request, Leave $leave)
    {
        $validated = $request->validate([
            'approval_notes' => 'nullable|string|max:500'
        ]);

        $leave->update([
            'status' => 'approved',
            'approved_by' => Auth::id(),
            'approved_at' => now(),
            'approval_notes' => $validated['approval_notes'] ?? null
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم الموافقة على طلب الإجازة بنجاح'
        ]);
    }

    /**
     * Reject a leave request
     */
    public function reject(Request $request, Leave $leave)
    {
        $validated = $request->validate([
            'approval_notes' => 'required|string|max:500'
        ]);

        $leave->update([
            'status' => 'rejected',
            'approved_by' => Auth::id(),
            'approved_at' => now(),
            'approval_notes' => $validated['approval_notes']
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم رفض طلب الإجازة'
        ]);
    }

    /**
     * Get employee leave balance
     */
    public function getLeaveBalance(Employee $employee)
    {
        // Calculate used leave days for current year
        $currentYear = Carbon::now()->year;

        $usedAnnualLeave = Leave::where('employee_id', $employee->id)
            ->where('type', 'annual')
            ->where('status', 'approved')
            ->whereYear('start_date', $currentYear)
            ->sum('days_requested');

        $usedSickLeave = Leave::where('employee_id', $employee->id)
            ->where('type', 'sick')
            ->where('status', 'approved')
            ->whereYear('start_date', $currentYear)
            ->sum('days_requested');

        // Default entitlements (should be configurable per employee/company policy)
        $annualEntitlement = 21; // 21 days annual leave
        $sickEntitlement = 30;   // 30 days sick leave

        return response()->json([
            'annual_balance' => max(0, $annualEntitlement - $usedAnnualLeave),
            'sick_balance' => max(0, $sickEntitlement - $usedSickLeave),
            'annual_used' => $usedAnnualLeave,
            'sick_used' => $usedSickLeave,
            'annual_entitlement' => $annualEntitlement,
            'sick_entitlement' => $sickEntitlement
        ]);
    }
}
