<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Employee extends Model
{
    protected $fillable = [
        'employee_id',
        'user_id',
        'department_id',
        'first_name',
        'last_name',
        'first_name_ar',
        'last_name_ar',
        'email',
        'phone',
        'national_id',
        'birth_date',
        'gender',
        'marital_status',
        'address',
        'position',
        'position_ar',
        'hire_date',
        'salary',
        'contract_type',
        'employment_status',
        'emergency_contact_name',
        'emergency_contact_phone',
        'bank_account',
        'bank_name',
        'profile_picture',
        'documents',
        'annual_leave_balance',
    ];

    protected function casts(): array
    {
        return [
            'birth_date' => 'date',
            'hire_date' => 'date',
            'salary' => 'decimal:2',
            'documents' => 'array',
            'annual_leave_balance' => 'integer',
        ];
    }

    /**
     * Get the full name attribute.
     */
    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Get the full Arabic name attribute.
     */
    public function getFullNameArAttribute()
    {
        return $this->first_name_ar . ' ' . $this->last_name_ar;
    }

    /**
     * Get the user that owns the employee.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the department that owns the employee.
     */
    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the attendances for the employee.
     */
    public function attendances()
    {
        return $this->hasMany(Attendance::class);
    }

    /**
     * Get the leaves for the employee.
     */
    public function leaves()
    {
        return $this->hasMany(Leave::class);
    }

    /**
     * Get the payrolls for the employee.
     */
    public function payrolls()
    {
        return $this->hasMany(Payroll::class);
    }

    /**
     * Get the performance reviews for the employee.
     */
    public function performanceReviews()
    {
        return $this->hasMany(PerformanceReview::class);
    }
}
