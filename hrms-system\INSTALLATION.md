# 🚀 دليل التثبيت - نظام إدارة الموارد البشرية

## متطلبات النظام

### الحد الأدنى من المتطلبات:
- **PHP**: 8.2 أو أحدث
- **Composer**: أحدث إصدار
- **قاعدة البيانات**: SQLite (افتراضي) أو MySQL
- **خادم الويب**: Apache أو Nginx (اختياري للتطوير)

### المتطلبات الموصى بها:
- **PHP**: 8.3
- **الذاكرة**: 512MB أو أكثر
- **مساحة القرص**: 100MB أو أكثر

## خطوات التثبيت

### 1. تحضير البيئة

```bash
# تأكد من تثبيت PHP و Composer
php --version
composer --version
```

### 2. تثبيت التبعيات

```bash
# الانتقال إلى مجلد المشروع
cd hrms-system

# تثبيت تبعيات PHP
composer install

# إنشاء ملف البيئة
copy .env.example .env

# توليد مفتاح التطبيق
php artisan key:generate
```

### 3. إعداد قاعدة البيانات

#### استخدام SQLite (الافتراضي):
```bash
# إنشاء ملف قاعدة البيانات
New-Item -ItemType File -Path "database/database.sqlite" -Force

# تشغيل الهجرات
php artisan migrate

# إضافة البيانات التجريبية
php artisan db:seed
```

#### استخدام MySQL (اختياري):
```bash
# تحديث ملف .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hrms_system
DB_USERNAME=root
DB_PASSWORD=your_password

# إنشاء قاعدة البيانات
mysql -u root -p -e "CREATE DATABASE hrms_system;"

# تشغيل الهجرات
php artisan migrate

# إضافة البيانات التجريبية
php artisan db:seed
```

### 4. تشغيل التطبيق

```bash
# تشغيل خادم التطوير
php artisan serve

# أو تحديد المنفذ
php artisan serve --port=8000
```

### 5. الوصول إلى التطبيق

افتح المتصفح وانتقل إلى: `http://localhost:8000`

## بيانات الدخول الافتراضية

### مدير النظام
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456
- **الصلاحيات**: جميع الصلاحيات

### موظف الموارد البشرية
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456
- **الصلاحيات**: إدارة الموظفين والحضور والإجازات

## إعدادات إضافية

### تكوين البريد الإلكتروني
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="نظام الموارد البشرية"
```

### تكوين رفع الملفات
```bash
# إنشاء رابط رمزي للتخزين
php artisan storage:link
```

### تحسين الأداء (للإنتاج)
```bash
# تحسين التطبيق
php artisan config:cache
php artisan route:cache
php artisan view:cache

# تحسين Composer
composer install --optimize-autoloader --no-dev
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ "Class not found"
```bash
composer dump-autoload
```

#### 2. خطأ في الصلاحيات
```bash
# Windows
icacls storage /grant Everyone:F /T
icacls bootstrap/cache /grant Everyone:F /T

# Linux/Mac
chmod -R 775 storage bootstrap/cache
```

#### 3. خطأ في قاعدة البيانات
```bash
# إعادة إنشاء قاعدة البيانات
php artisan migrate:fresh --seed
```

#### 4. خطأ في المفتاح
```bash
php artisan key:generate
```

## التحديث

### تحديث التطبيق:
```bash
# سحب آخر التحديثات
git pull origin main

# تحديث التبعيات
composer update

# تشغيل الهجرات الجديدة
php artisan migrate

# مسح الكاش
php artisan cache:clear
php artisan config:clear
php artisan view:clear
```

## النسخ الاحتياطي

### إنشاء نسخة احتياطية:
```bash
# نسخ احتياطي لقاعدة البيانات (SQLite)
copy database\database.sqlite backup\database_backup_$(Get-Date -Format "yyyy-MM-dd").sqlite

# نسخ احتياطي للملفات المرفوعة
xcopy storage\app\public backup\storage /E /I
```

### استعادة النسخة الاحتياطية:
```bash
# استعادة قاعدة البيانات
copy backup\database_backup_2025-07-23.sqlite database\database.sqlite

# استعادة الملفات
xcopy backup\storage storage\app\public /E /I
```

## الأمان

### تحسينات الأمان للإنتاج:

1. **تغيير كلمات المرور الافتراضية**
2. **تفعيل HTTPS**
3. **تحديث APP_ENV إلى production**
4. **تفعيل APP_DEBUG=false**
5. **استخدام كلمات مرور قوية لقاعدة البيانات**

### ملف .env للإنتاج:
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_DATABASE=your-db-name
DB_USERNAME=your-db-user
DB_PASSWORD=strong-password
```

## الدعم

إذا واجهت أي مشاكل:

1. تحقق من ملف `storage/logs/laravel.log`
2. تأكد من تطبيق جميع خطوات التثبيت
3. راجع المتطلبات والإعدادات
4. اتصل بفريق الدعم

---

**تم إنشاء هذا الدليل لضمان تثبيت سلس وآمن للنظام** 🔧
