<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PerformanceReview extends Model
{
    protected $fillable = [
        'employee_id',
        'reviewer_id',
        'review_period',
        'review_date',
        'quality_score',
        'productivity_score',
        'teamwork_score',
        'communication_score',
        'leadership_score',
        'overall_score',
        'strengths',
        'areas_for_improvement',
        'goals',
        'comments',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'review_date' => 'date',
            'quality_score' => 'integer',
            'productivity_score' => 'integer',
            'teamwork_score' => 'integer',
            'communication_score' => 'integer',
            'leadership_score' => 'integer',
            'overall_score' => 'decimal:1',
        ];
    }

    /**
     * Get the employee that owns the performance review.
     */
    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the reviewer (user) who conducted the review.
     */
    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }
}
