<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payrolls', function (Blueprint $table) {
            // Drop old columns if they exist
            if (Schema::hasColumn('payrolls', 'payroll_period')) {
                $table->dropColumn('payroll_period');
            }
            if (Schema::hasColumn('payrolls', 'overtime_amount')) {
                $table->dropColumn('overtime_amount');
            }
            if (Schema::hasColumn('payrolls', 'tax_deduction')) {
                $table->dropColumn('tax_deduction');
            }
            if (Schema::hasColumn('payrolls', 'insurance_deduction')) {
                $table->dropColumn('insurance_deduction');
            }
            if (Schema::hasColumn('payrolls', 'working_days')) {
                $table->dropColumn('working_days');
            }
            if (Schema::hasColumn('payrolls', 'present_days')) {
                $table->dropColumn('present_days');
            }
            if (Schema::hasColumn('payrolls', 'absent_days')) {
                $table->dropColumn('absent_days');
            }
            if (Schema::hasColumn('payrolls', 'payment_date')) {
                $table->dropColumn('payment_date');
            }

            // Add new columns
            if (!Schema::hasColumn('payrolls', 'month')) {
                $table->integer('month')->after('employee_id');
            }
            if (!Schema::hasColumn('payrolls', 'year')) {
                $table->integer('year')->after('month');
            }
            if (!Schema::hasColumn('payrolls', 'overtime_rate')) {
                $table->decimal('overtime_rate', 8, 2)->default(0)->after('overtime_hours');
            }
            if (!Schema::hasColumn('payrolls', 'overtime_pay')) {
                $table->decimal('overtime_pay', 10, 2)->default(0)->after('overtime_rate');
            }
            if (!Schema::hasColumn('payrolls', 'created_by')) {
                $table->foreignId('created_by')->nullable()->constrained('users')->after('notes');
            }
            if (!Schema::hasColumn('payrolls', 'approved_by')) {
                $table->foreignId('approved_by')->nullable()->constrained('users')->after('created_by');
            }
            if (!Schema::hasColumn('payrolls', 'approved_at')) {
                $table->timestamp('approved_at')->nullable()->after('approved_by');
            }
            if (!Schema::hasColumn('payrolls', 'approval_notes')) {
                $table->text('approval_notes')->nullable()->after('approved_at');
            }

            // Update existing columns
            $table->string('status')->default('pending')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payrolls', function (Blueprint $table) {
            // Add back old columns
            $table->string('payroll_period')->nullable();
            $table->decimal('overtime_amount', 10, 2)->default(0);
            $table->decimal('tax_deduction', 10, 2)->default(0);
            $table->decimal('insurance_deduction', 10, 2)->default(0);
            $table->integer('working_days')->default(0);
            $table->integer('present_days')->default(0);
            $table->integer('absent_days')->default(0);
            $table->date('payment_date')->nullable();

            // Drop new columns
            $table->dropColumn([
                'month',
                'year',
                'overtime_rate',
                'overtime_pay',
                'created_by',
                'approved_by',
                'approved_at',
                'approval_notes'
            ]);
        });
    }
};
