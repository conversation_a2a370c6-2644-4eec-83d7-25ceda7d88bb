<?php

namespace App\Http\Controllers;

use App\Models\Payroll;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class PayrollController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Payroll::with(['employee.department']);
        
        // Apply filters
        if ($request->filled('month')) {
            $query->where('month', $request->month);
        }
        
        if ($request->filled('year')) {
            $query->where('year', $request->year);
        }
        
        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        $payrolls = $query->orderBy('year', 'desc')
                         ->orderBy('month', 'desc')
                         ->paginate(15);
        
        // Get statistics
        $stats = [
            'total_payrolls' => $payrolls->total(),
            'pending_payrolls' => Payroll::where('status', 'pending')->count(),
            'approved_payrolls' => Payroll::where('status', 'approved')->count(),
            'total_amount' => Payroll::where('status', 'approved')->sum('net_salary')
        ];
        
        return view('payroll.index', compact('payrolls', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $employees = Employee::where('employment_status', 'active')
                           ->orderBy('first_name')
                           ->get();
        
        return view('payroll.create', compact('employees'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'month' => 'required|integer|between:1,12',
            'year' => 'required|integer|min:2020|max:2030',
            'basic_salary' => 'required|numeric|min:0',
            'allowances' => 'nullable|numeric|min:0',
            'overtime_hours' => 'nullable|numeric|min:0',
            'overtime_rate' => 'nullable|numeric|min:0',
            'deductions' => 'nullable|numeric|min:0',
            'bonus' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000'
        ]);

        // Check if payroll already exists for this employee and month
        $exists = Payroll::where('employee_id', $validated['employee_id'])
                        ->where('month', $validated['month'])
                        ->where('year', $validated['year'])
                        ->exists();

        if ($exists) {
            return redirect()->back()
                           ->withErrors(['employee_id' => 'راتب هذا الموظف لهذا الشهر موجود بالفعل'])
                           ->withInput();
        }

        // Calculate totals
        $overtimePay = ($validated['overtime_hours'] ?? 0) * ($validated['overtime_rate'] ?? 0);
        $grossSalary = $validated['basic_salary'] + ($validated['allowances'] ?? 0) + $overtimePay + ($validated['bonus'] ?? 0);
        $netSalary = $grossSalary - ($validated['deductions'] ?? 0);

        $validated['overtime_pay'] = $overtimePay;
        $validated['gross_salary'] = $grossSalary;
        $validated['net_salary'] = $netSalary;
        $validated['status'] = 'pending';
        $validated['created_by'] = Auth::id();

        Payroll::create($validated);

        return redirect()->route('payroll.index')
                        ->with('success', 'تم إنشاء راتب الموظف بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Payroll $payroll)
    {
        $payroll->load(['employee.department', 'createdBy', 'approvedBy']);
        
        // Get employee's payroll history
        $payrollHistory = Payroll::where('employee_id', $payroll->employee_id)
                                ->where('id', '!=', $payroll->id)
                                ->orderBy('year', 'desc')
                                ->orderBy('month', 'desc')
                                ->limit(6)
                                ->get();
        
        return view('payroll.show', compact('payroll', 'payrollHistory'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Payroll $payroll)
    {
        // Only allow editing if payroll is pending
        if ($payroll->status !== 'pending') {
            return redirect()->route('payroll.show', $payroll)
                           ->with('error', 'لا يمكن تعديل الراتب بعد الموافقة عليه');
        }
        
        $employees = Employee::where('employment_status', 'active')
                           ->orderBy('first_name')
                           ->get();
        
        return view('payroll.edit', compact('payroll', 'employees'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Payroll $payroll)
    {
        // Only allow updating if payroll is pending
        if ($payroll->status !== 'pending') {
            return redirect()->route('payroll.show', $payroll)
                           ->with('error', 'لا يمكن تعديل الراتب بعد الموافقة عليه');
        }

        $validated = $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'month' => 'required|integer|between:1,12',
            'year' => 'required|integer|min:2020|max:2030',
            'basic_salary' => 'required|numeric|min:0',
            'allowances' => 'nullable|numeric|min:0',
            'overtime_hours' => 'nullable|numeric|min:0',
            'overtime_rate' => 'nullable|numeric|min:0',
            'deductions' => 'nullable|numeric|min:0',
            'bonus' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:1000'
        ]);

        // Check if payroll already exists for this employee and month (excluding current)
        $exists = Payroll::where('employee_id', $validated['employee_id'])
                        ->where('month', $validated['month'])
                        ->where('year', $validated['year'])
                        ->where('id', '!=', $payroll->id)
                        ->exists();

        if ($exists) {
            return redirect()->back()
                           ->withErrors(['employee_id' => 'راتب هذا الموظف لهذا الشهر موجود بالفعل'])
                           ->withInput();
        }

        // Calculate totals
        $overtimePay = ($validated['overtime_hours'] ?? 0) * ($validated['overtime_rate'] ?? 0);
        $grossSalary = $validated['basic_salary'] + ($validated['allowances'] ?? 0) + $overtimePay + ($validated['bonus'] ?? 0);
        $netSalary = $grossSalary - ($validated['deductions'] ?? 0);

        $validated['overtime_pay'] = $overtimePay;
        $validated['gross_salary'] = $grossSalary;
        $validated['net_salary'] = $netSalary;

        $payroll->update($validated);

        return redirect()->route('payroll.show', $payroll)
                        ->with('success', 'تم تحديث راتب الموظف بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Payroll $payroll)
    {
        $payroll->delete();

        return redirect()->route('payroll.index')
                        ->with('success', 'تم حذف راتب الموظف بنجاح');
    }

    /**
     * Approve a payroll
     */
    public function approve(Request $request, Payroll $payroll)
    {
        $validated = $request->validate([
            'approval_notes' => 'nullable|string|max:500'
        ]);

        $payroll->update([
            'status' => 'approved',
            'approved_by' => Auth::id(),
            'approved_at' => now(),
            'approval_notes' => $validated['approval_notes'] ?? null
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم الموافقة على الراتب بنجاح'
        ]);
    }

    /**
     * Reject a payroll
     */
    public function reject(Request $request, Payroll $payroll)
    {
        $validated = $request->validate([
            'approval_notes' => 'required|string|max:500'
        ]);

        $payroll->update([
            'status' => 'rejected',
            'approved_by' => Auth::id(),
            'approved_at' => now(),
            'approval_notes' => $validated['approval_notes']
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم رفض الراتب'
        ]);
    }

    /**
     * Generate payroll for all employees
     */
    public function generateAll(Request $request)
    {
        $validated = $request->validate([
            'month' => 'required|integer|between:1,12',
            'year' => 'required|integer|min:2020|max:2030'
        ]);

        $employees = Employee::where('employment_status', 'active')->get();
        $generated = 0;
        $skipped = 0;

        foreach ($employees as $employee) {
            // Check if payroll already exists
            $exists = Payroll::where('employee_id', $employee->id)
                            ->where('month', $validated['month'])
                            ->where('year', $validated['year'])
                            ->exists();

            if (!$exists) {
                // Calculate basic salary and totals
                $basicSalary = $employee->salary ?? 0;
                $grossSalary = $basicSalary;
                $netSalary = $grossSalary;

                Payroll::create([
                    'employee_id' => $employee->id,
                    'month' => $validated['month'],
                    'year' => $validated['year'],
                    'basic_salary' => $basicSalary,
                    'allowances' => 0,
                    'overtime_hours' => 0,
                    'overtime_rate' => 0,
                    'overtime_pay' => 0,
                    'deductions' => 0,
                    'bonus' => 0,
                    'gross_salary' => $grossSalary,
                    'net_salary' => $netSalary,
                    'status' => 'pending',
                    'created_by' => Auth::id()
                ]);

                $generated++;
            } else {
                $skipped++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "تم إنشاء {$generated} راتب، تم تخطي {$skipped} راتب موجود مسبقاً"
        ]);
    }
}
